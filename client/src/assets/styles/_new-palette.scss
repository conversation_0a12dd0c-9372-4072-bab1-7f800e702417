// Main Color Palette
$main-colors: (
  50: #FFF6E0,
  100: #FEF1CF,
  150: #FFECBF,
  200: #FFE6AE,
  250: #EDC17F,
  300: #DA9C50,
  400: #BA7A2C,
  500: #99601A,
  600: #532E00,
  700: #351F04,
  800: #261604,
  900: #190E01
);

// Function to get main colors
@function main($shade) {
  @return map-get($main-colors, $shade);
}

// Create CSS Custom Properties
:root {
  @each $shade, $color in $main-colors {
    --main-#{$shade}: #{$color};
  }
}

// Optional: Create utility classes
.bg-main {
  @each $shade, $color in $main-colors {
    &-#{$shade} {
      background-color: $color;
    }
  }
}

.text-main {
  @each $shade, $color in $main-colors {
    &-#{$shade} {
      color: $color;
    }
  }
}