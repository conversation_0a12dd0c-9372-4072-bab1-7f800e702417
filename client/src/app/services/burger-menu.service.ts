import { Injectable, signal } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class BurgerMenuService {
  private readonly _isBurgerMenuOpen = signal<boolean>(false);

  // Публичный readonly сигнал для чтения состояния
  readonly isBurgerMenuOpen = this._isBurgerMenuOpen.asReadonly();

  /**
   * Открыть бургер-меню
   */
  openBurgerMenu(): void {
    this._isBurgerMenuOpen.set(true);
  }

  /**
   * Закрыть бургер-меню
   */
  closeBurgerMenu(): void {
    this._isBurgerMenuOpen.set(false);
  }

  /**
   * Переключить состояние бургер-меню
   */
  toggleBurgerMenu(): void {
    this._isBurgerMenuOpen.set(!this._isBurgerMenuOpen());
  }
}
