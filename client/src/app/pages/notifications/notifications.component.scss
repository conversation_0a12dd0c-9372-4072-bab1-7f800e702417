.advertisements-list {
  margin-top: 30px;
}

.no-items {
  text-align: center;
  padding: 60px 30px;
  background: linear-gradient(135deg, var(--light-color, #f8f9fa) 0%, var(--side_back, #e9ecef) 100%);
  border-radius: 20px;
  border: 1px solid var(--border, #dee2e6);

  p {
    font-family: 'Prata', serif;
    font-size: 1.1rem;
    color: var(--text-color, #333);
    margin: 0;
  }
}

.ads-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.ad-card {
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 12px var(--shadow, rgba(0, 0, 0, 0.1));
  border: 1px solid var(--border, #dee2e6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 32px var(--shadow, rgba(0, 0, 0, 0.15));
    border-color: var(--text-color, #333);

    .ad-image {
      transform: scale(1.05);
    }

    .ad-link-indicator {
      opacity: 1;
      transform: translateX(0);
    }
  }

  &:active {
    transform: translateY(-4px);
  }
}

.delete-notification-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--border, #dee2e6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  backdrop-filter: blur(4px);

  &:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #dc3545;
    transform: scale(1.1);

    svg {
      color: #dc3545;
    }
  }

  &:active {
    transform: scale(0.95);
  }

  svg {
    color: var(--text-color, #333);
    transition: color 0.2s ease;
  }

  @media (max-width: 768px) {
    width: 28px;
    height: 28px;
    top: 8px;
    right: 8px;

    svg {
      width: 14px;
      height: 14px;
    }
  }
}

.ad-image-container {
  position: relative;
  width: 100%;
  height: 220px;
  overflow: hidden;
  background: linear-gradient(135deg, var(--light-color, #f8f9fa) 0%, var(--side_back, #e9ecef) 100%);
  display: block !important; // Force display regardless of image errors

  .ad-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .ad-image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--text-color, #333);
    background: linear-gradient(135deg, var(--light-color, #f8f9fa) 0%, var(--side_back, #e9ecef) 100%);

    svg {
      opacity: 0.6;
    }
  }
}

.ad-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ad-title {
  font-family: 'Prata', serif;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.4;
  color: var(--font-color1, #333);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ad-description {
  font-family: 'Prata', serif;
  font-size: 0.95rem;
  line-height: 1.6;
  color: var(--text-color23, #666);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
}

.ad-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid var(--border, #dee2e6);
}

.ad-date {
  font-family: 'Prata', serif;
  font-size: 0.85rem;
  color: var(--text-color, #333);
  margin: 0;
}

.ad-link-indicator {
  font-family: 'Prata', serif;
  font-size: 0.9rem;
  font-weight: 400;
  color: var(--text-color, #333);
  opacity: 0.7;
  transform: translateX(-8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  @media (max-width: 480px) {
    font-size: 0.85rem;
  }
}

// Responsive adjustments
@media (max-width: 1024px) {
  .ads-grid {
    gap: 20px;
  }

  .ad-image-container {
    height: 200px;
  }

  .ad-content {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .ad-image-container {
    height: 180px;
  }

  .ad-content {
    padding: 18px;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .ad-card {
    border-radius: 16px;
  }

  .ad-image-container {
    height: 160px;
  }

  .ad-content {
    padding: 16px;
  }

  .no-items {
    padding: 40px 20px;
    border-radius: 16px;
  }
}

