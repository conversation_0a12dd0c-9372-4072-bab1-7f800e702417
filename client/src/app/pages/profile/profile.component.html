<div class="middle_stripe">
  <breadcrumb></breadcrumb>

  <dialog class="stylized_wide" #confirmDialog>
    <div class="dialog-message">
      {{ message }}
    </div>
    <div class="mt-[60px] flex gap-[16px] text-center justify-center dialog-footer">
      <button type="submit" class="confirm-btn ok-button">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="confirm-btn-label">Да</div>
      </button>
      <button type="submit" class="confirm-btn cancel-button">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
        <div class="confirm-btn-label">Отмена</div>
      </button>
    </div>
  </dialog>

  @if(profileService.profile) {
  <div class="tab-container profile relative">
    <div class="wrapper_line custom_">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Намасте{{profileService.profile.firstName || profileService.profile.lastName ||
          profileService.profile.spiritualName ? ',' :
          ''}} {{profileService.profile.spiritualName ? profileService.profile.spiritualName :
          (profileService.profile.firstName + ' ' + profileService.profile.lastName)}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
    </div>
    <div class="flex relative justify-center tabs_w" #tabs_w>
      <div class="profile-tabs">
        <ng-container *ngFor="let tab of profileTabs">
          <div class="profile-tab" [ngClass]="{'is-active': activeTab === tab }" (click)="selectTab(tab)">
            {{tab}}
          </div>
        </ng-container>
      </div>
    </div>
    <!-- <div class="profile-links mt-5 mb-5 flex justify-center">
        <a class="btn btn-primary" [routerLink]="['/ru/profile/favourites']" href="#">Избранное</a>
        <a class="btn btn-primary" [routerLink]="['/ru/profile/playlist']" href="#">Плейлист</a>
        <a class="btn btn-primary" (click)="$event.preventDefault(); formTab = true" href="#">Анкета</a>
      </div> -->
  </div>
  }
</div>
<div class="line_th"></div>
<div class="middle_stripe">
  @if(profileService.profile) {
  <div class="tab-container profile relative">
    <div class="tab-content" [ngSwitch]="activeTab">
      <div class="flex w-full" *ngSwitchCase="ProfileTabs.MY_DATA">
        <app-my-data class="grow"></app-my-data>
      </div>
      <div class="flex w-full" *ngSwitchCase="ProfileTabs.FAVORITES">
        <app-favourites class="grow"></app-favourites>
      </div>
      <div class="flex w-full" *ngSwitchCase="ProfileTabs.PLAYLISTS">
        <app-playlist class="grow"></app-playlist>
      </div>

      <div class="flex flex-col items-center w-full" *ngSwitchCase="ProfileTabs.SUBSCRIPTIONS">
        <div class="w-full">
          <button class="btn_pcj_">Добавить подписку</button>
          <div class="subscriptions-active" *ngIf='subscriptions'>
            <!-- <div>Активные подписки:</div> -->
            <div class="card_pcj_wrap">
              <div class="card_pcj_">
                <div>
                  <div class="title_pcj_wp">
                    <div class="title_pcj">Аудиоподписка</div>
                  </div>
                  <div>
                    <span class="price_pcj">€12</span>
                    <span class="per-month_pcj">/месяц</span>
                  </div>
                </div>
                <div class="dsci_wrapper">
                  <div class="pres_wr discount_active">
                    Активная
                  </div>
                  <div class="flex items-center">
                    <div class="nx_pt">Следующий платеж:</div>
                    <div class="nx_ptg">&nbsp;&nbsp;7 сентября 2025</div>
                  </div>
                  <div class="nx_pti">Остановить подписку</div>
                </div>
              </div>
              <div class="card_pcj_">
                <div>
                  <div class="title_pcj_wp">
                    <div class="title_pcj">Библиотека</div>
                  </div>
                  <div>
                    <span class="price_pcj">€15</span>
                    <span class="per-month_pcj">/месяц</span>
                  </div>
                </div>
                <div class="dsci_wrapper">
                  <div class="pres_wr discount_active">
                    Активная
                  </div>
                  <div class="flex items-center">
                    <div class="nx_pt">Следующий платеж:</div>
                    <div class="nx_ptg">&nbsp;&nbsp;7 сентября 2025</div>
                  </div>
                  <div class="nx_pti">Остановить подписку</div>
                </div>
              </div>
            </div>
            <div class="ttl_wrap">
              <div class="flex items-end">
                <div class="title_pcj">Всего:&nbsp;&nbsp;</div>
                <div>
                  <span class="price_pcj">€27</span>
                  <span class="per-month_pcj">/месяц</span>
                </div>
              </div>
              <div class="stp_s">Остановить все подписки</div>
            </div>
            <div class="price_pcj mauto">История платежей</div>
            <div class="tabl_wrp">
              <table class="custom-table">
                <thead>
                  <tr>
                    <th>Статус</th>
                    <th>Дата</th>
                    <th>Способ оплаты</th>
                    <th>Детали</th>
                    <th>Сумма</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><span class="badge pending">В обработке</span></td>
                    <td>07.08.2025<br><small>14:03</small></td>
                    <td class="card-icon_tx"><img src="assets/images/Card_vsa.svg" alt="VISA"
                        class="card-icon"> &nbsp;&nbsp;4515*****6798</td>
                    <td class="td_txt">Курсы и обучение</td>
                    <td>€25</td>
                  </tr>
                  <tr>
                    <td><span class="badge active">Активная</span></td>
                    <td>07.08.2025<br><small>14:03</small></td>
                    <td class="card-icon_tx"><img src="assets/images/Card_vsa.svg" alt="VISA"
                        class="card-icon"> &nbsp;&nbsp;4515*****6798</td>
                    <td class="td_txt">Библиотека</td>
                    <td>€15</td>
                  </tr>
                  <tr>
                    <td><span class="badge error">Ошибка</span></td>
                    <td>07.08.2025<br><small>14:03</small></td>
                    <td class="card-icon_tx"><img src="assets/images/Card_vsa.svg" alt="VISA"
                        class="card-icon"> &nbsp;&nbsp;4515*****6798</td>
                    <td class="td_txt">Аудиоподписка + ИИ чат "Вивека"</td>
                    <td>€17</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="load_more_wrap">
              <div class="lod_m"><img src="assets/images/icons/Left Icon.svg" alt="arr">Загрузить еще</div>
            </div>
            <!-- <div class="subscriptions-list">
              <div class="subscription-item" *ngFor="let sub of profileService.profile.subscriptions">
                <span *ngIf='subscriptions[$any(sub).type]'>
                  {{subscriptions[$any(sub).type].name}} до {{activeUntil($any(sub))}} 
                  <span *ngIf="$any(sub).cardNumber">(карта *{{$any(sub).cardNumber}})</span>
                </span>
                <button style='margin-left: 20px;' *ngIf="$any(sub).isAutoRenew"
                        class="cancel-btn"
                        (click)="cancelAutoRenew($any(sub))">
                  Отключить автопродление
                </button>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>

  </div>
  }
</div>