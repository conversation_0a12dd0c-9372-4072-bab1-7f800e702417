import { CommonModule } from '@angular/common'
import { AfterViewChecked, AfterViewInit, ChangeDetectionStrategy, Component, computed, ElementRef, input, output, signal, ViewChild } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { Chat, ChatSource } from '../../ai-chat.component'
import { ChatMessageComponent } from '../chat-message/chat-message.component'
import { debounceTime, Subject, take, timer } from 'rxjs'

@Component({
  selector: 'app-chat-main',
  standalone: true,
  imports: [CommonModule, FormsModule, ChatMessageComponent],
  templateUrl: './chat-main.component.html',
  styleUrl: './chat-main.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChatMainComponent implements AfterViewChecked {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  // Inputs
  chat = input<Chat | null>(null);
  isLoading = input<boolean>(false);

  // Outputs
  sendMessage = output<string>();
  sourceClick = output<ChatSource>();
  typewritingComplete = output<string>();

  // State
  messageText = signal<string>('');
  showScrollDownButton = signal(false);

  scrollToBottom$: Subject<any> = new Subject();

  // Computed
  hasMessages = computed(() => {
    const currentChat = this.chat();
    return currentChat && currentChat.messages && currentChat.messages.length > 0;
  });

  chatTitle = computed(() => {
    const currentChat = this.chat();
    if (!currentChat) return '';

    if (currentChat.title && currentChat.title.trim()) {
      return currentChat.title;
    }

    // Generate title from first user message
    if (currentChat.messages && currentChat.messages.length > 0) {
      const firstUserMessage = currentChat.messages.find(m => m.role === 'user');
      if (firstUserMessage) {
        const title = firstUserMessage.content.slice(0, 50);
        return title.length < firstUserMessage.content.length ? title + '...' : title;
      }
    }

    return 'Новый чат';
  });

  constructor() {
    // For single scroll to bottom and single addListener for block
    //IMPORTANT! IF USE ngAfterViewChecked its create multisubscriptions!
    this.scrollToBottom$.pipe(debounceTime(400), take(1)).subscribe(() => {
      this.scrollToBottom();
      this.messagesContainer?.nativeElement?.addEventListener('scroll', () => {
        this.updateScrollButtonVisibility();
      });
    })
  }

  ngAfterViewChecked() {
    this.scrollToBottom$.next(true);
  }

  onSendMessage() {
    const text = this.messageText().trim();
    if (!text || this.isLoading()) return;

    this.sendMessage.emit(text);
    this.messageText.set('');
    
    // Reset textarea height
    if (this.messageInput?.nativeElement) {
      this.messageInput.nativeElement.style.height = 'auto';
    }
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.onSendMessage();
    }
  }

  onInput(event: Event) {
    const textarea = event.target as HTMLTextAreaElement;
    
    // Auto-resize textarea
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  }

  onSourceClick(source: ChatSource) {
    this.sourceClick.emit(source);
  }

  onTypewritingComplete(messageId: string) {
    this.typewritingComplete.emit(messageId);
  }

  scrollToBottom() {
    if (this.messagesContainer?.nativeElement) {
      const container = this.messagesContainer.nativeElement;
      container.scrollTop = container.scrollHeight;
    }
  }

  getLoadingText(): string {
    const loadingStates = [
      'Обрабатываю ваш запрос...',
      'Анализирую источники...',
      'Формирую ответ...'
    ];
    
    // Cycle through loading states every 2 seconds
    const index = Math.floor(Date.now() / 2000) % loadingStates.length;
    return loadingStates[index];
  }

  private updateScrollButtonVisibility() {
    const container = this.messagesContainer.nativeElement;
    const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
    this.showScrollDownButton.set(!isAtBottom);
  }
}
