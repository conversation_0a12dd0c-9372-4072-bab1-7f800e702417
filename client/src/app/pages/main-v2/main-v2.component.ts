import { AccentCardComponent } from '@/components/cards/accent-card/accent-card.component';
import { CardWithDomeComponent } from '@/components/cards/card-with-dome/card-with-dome.component';
import { ChronologicalCardComponent } from '@/components/cards/chronological-card/chronological-card.component';
import { CarouselV2Component } from '@/components/carousel-v2/carousel-v2.component';
import { Component, inject, PLATFORM_ID, HostListener, signal, ViewChild, ElementRef } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Levels } from '@/enums/levels.enum';
import { JoinCardComponent } from '@/components/cards/join-card/join-card.component';
import { DailySidebarComponent } from '@/components/daily-sidebar/daily-sidebar.component';
import { ScaledCarouselComponent } from '@/components/scaled-carousel/scaled-carousel.component';

@Component({
  selector: 'app-main-v2',
  imports: [CarouselV2Component, ChronologicalCardComponent, AccentCardComponent, CardWithDomeComponent, JoinCardComponent,
     ScaledCarouselComponent

  ],
  templateUrl: './main-v2.component.html',
  standalone: true,
  styleUrl: './main-v2.component.scss'
})
export class MainV2Component {
  itemsPerView = 4;

  

  private platformId = inject(PLATFORM_ID);
  @ViewChild('projectsTabs') projectsTabs!: ElementRef;


  isDarkMarker = false;

  events = [
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    },
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    },
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    },
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    },
    {
      name: 'День Карма-йоги: Служение общине',
      description: 'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
      date: '15-20 мая 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
    }
  ];

  courses = [
    {
      name: 'Основы медитации 1 first',
      description: '111Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Принять_Символ_Веры.webp',
      level: Levels.ENTRY
    },
    {
      name: 'Йога сутры Патанджали 1',
      description: '222Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Принять_дикшу.webp',
      level: Levels.INTERMEDIATE
    },
    {
      name: 'Адвайта Веданта 1',
      description: '333Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Стать_монахом.webp',
      level: Levels.ADVANCED
    },
    {
      name: 'Основы медитации 2',
      description: '444Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Приехать_в_Ашрам.webp',
      level: Levels.ENTRY
    },
    {
      name: 'Основы медитации 3',
      description: '555Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Заказать_ритуал.webp',
      level: Levels.ENTRY
    },
    {
      name: 'Адвайта Веданта 2',
      description: '666Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      level: Levels.ADVANCED
    },
    {
      name: 'Йога сутры Патанджали 1',
      description: '777Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Центр.webp',
      level: Levels.INTERMEDIATE
    },
    {
      name: 'Основы медитации 4',
      description: '888Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      level: Levels.ENTRY
    },
    {
      name: 'Основы медитации 5 last',
      description: '9999Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/iryna_it_cinematic_wide_angle_shot_of_a_beautiful_Hindu_temple__3cd826ee-783a-44af-a6b8-8227a4935d7b 1.webp',
      level: Levels.ENTRY
    }
  ];

  joinCards = [
    {
      name: 'Принять Символ Веры',
      description: 'Вступление в традицию',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Принять_Символ_Веры.webp',
    },
    {
      name: 'Принять дикшу',
      description: 'Посвящение',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Принять_дикшу.webp',
    },
    {
      name: 'Стать монахом',
      description: 'Жизнь Садху',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Стать_монахом.webp',
    },
    {
      name: 'Приехать в Ашрам',
      description: 'Участие в ретрите и служении',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Приехать_в_Ашрам.webp',
    },
    {
      name: 'Заказать ритуал',
      description: 'Поддержка и благословение',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Заказать_ритуал.webp',
    },
    {
      name: 'Создать Ашрам / Центр',
      description: 'Расширение миссии',
      date: '24 сентября',
      imageUrl: '/assets/images/main-v2/default-pictures/Центр.webp',
    }
  ];

  projectTabs = [
    {
      title: 'Образовательная программа',
      description: 'Бесплатные занятия по йоге, медитации и философии для детей и взрослых в различных городах.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Центр.webp',
      link: '/education'
    },
    {
      title: 'Экологическая инициатива',
      description: 'Бесплатные занятия по йоге, медитации и философии для детей и взрослых в различных городах.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Заказать_ритуал.webp',
      link: '/education'
    },
    {
      title: 'Гуманитарная помощь',
      description: 'Бесплатные занятия по йоге, медитации и философии для детей и взрослых в различных городах.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Приехать_в_Ашрам.webp',
      link: '/education'
    },
    {
      title: 'Институт Васиштхи',
      description: 'Бесплатные занятия по йоге, медитации и философии для детей и взрослых в различных городах.',
      date: '24 сентября 2025',
      imageUrl: '/assets/images/main-v2/default-pictures/Sculpture_of_Ganesha_in_Berlin_Museum.jpg',
      link: '/education'
    },
  ];

  selectedCourse = signal(this.courses[0] || null);

  activeProjectTab = this.projectTabs[0];


  @HostListener('window:resize')
  onResize(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.checkScreenSize();
    }
  }
  //   @HostListener('window:scroll', [])
  // private checkScroll(): void {
  //   if (isPlatformBrowser(this.platformId)) {
  //     this.isScrolled = window.scrollY > 20;
  //     const halfHeightScreen = window.innerHeight / 2;
  //     this.isDarkMarker = window.scrollY > halfHeightScreen && document.body.scrollHeight - window.scrollY > 1278;
  //   }
  // }

  // ngOnInit(): void {
  //   this.checkScroll();
  // }

    
    ngOnInit(): void {
   
    this.checkScreenSize();
  }

  ngAfterViewInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.setupHorizontalScroll();
    }
  }

  private setupHorizontalScroll(): void {
    if (this.projectsTabs) {
      const tabsElement = this.projectsTabs.nativeElement;
      tabsElement.addEventListener('wheel', (e: WheelEvent) => {
        // Проверяем, что есть горизонтальный скролл и прокрутка идет вертикально
        if (tabsElement.scrollWidth > tabsElement.clientWidth && e.deltaY !== 0) {
          e.preventDefault();
          e.stopPropagation();
          tabsElement.scrollLeft += e.deltaY;
        }
      }, { passive: false });
    }
  }

  checkScreenSize(): void {
    if (isPlatformBrowser(this.platformId)) {
      const width = window.innerWidth;
      if (width < 650) {
        this.itemsPerView = 2;
      } else if (width < 1100) {
        this.itemsPerView = 3;
      } else {
        this.itemsPerView = 4;
      }
    }
  }

  selectedCourseChange(course: any) {
    this.selectedCourse.set(course);
  }



  setActiveProjectTab(tab: any): void {
    this.activeProjectTab = tab;

    this.scrollToActiveTab(tab);
  }

  private scrollToActiveTab(activeTab: any): void {
    if (!this.projectsTabs) return;

    const tabsContainer = this.projectsTabs.nativeElement;
    const tabElements = tabsContainer.querySelectorAll('.tab-item');

    const activeTabIndex = this.projectTabs.findIndex(tab => tab.title === activeTab.title);

    if (activeTabIndex !== -1 && tabElements[activeTabIndex]) {
      const activeTabElement = tabElements[activeTabIndex] as HTMLElement;

      const tabLeft = activeTabElement.offsetLeft;
      const tabWidth = activeTabElement.offsetWidth;
      const containerWidth = tabsContainer.clientWidth;
      const currentScroll = tabsContainer.scrollLeft;

      const targetScroll = tabLeft - (containerWidth / 2) + (tabWidth / 2);

      const maxScroll = tabsContainer.scrollWidth - containerWidth;
      const finalScroll = Math.max(0, Math.min(targetScroll, maxScroll));

      tabsContainer.scrollTo({
        left: finalScroll,
        behavior: 'smooth'
      });
    }
  }
}
