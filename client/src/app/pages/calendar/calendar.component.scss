@use ".../../../../styles/core.scss" as core;
@use "../categories/category/category.component.scss";
// Calendar page styling following site design patterns
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.calendar-content {
  margin-top: 30px;
}

// Разделитель между месяцами
.month-separator {
  margin: 50px 0 40px 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .separator-line {
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      var(--border, #dee2e6) 20%, 
      var(--text-color, #333) 50%, 
      var(--border, #dee2e6) 80%, 
      transparent 100%
    );
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 12px;
      height: 12px;
      background: var(--text-color, #333);
      border-radius: 50%;
      box-shadow: 0 0 0 4px var(--light-color, #f8f9fa);
    }
  }

  @media (max-width: 768px) {
    margin: 40px 0 30px 0;
  }

  @media (max-width: 480px) {
    margin: 30px 0 20px 0;
  }
}

// Заголовок месяца
.month-header {
  margin-bottom: 30px;
  text-align: center;

  .month-title {
    font-family: 'Prata', serif;
    font-size: 1.8rem;
    font-weight: 400;
    color: var(--text-color, #333);
    margin: 0;
    text-transform: capitalize;
    position: relative;
    display: inline-block;

    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 1px;
      background: var(--text-color, #333);
      opacity: 0.3;
    }

    @media (max-width: 768px) {
      font-size: 1.6rem;
    }

    @media (max-width: 480px) {
      font-size: 1.4rem;
    }
  }

  @media (max-width: 768px) {
    margin-bottom: 25px;
  }

  @media (max-width: 480px) {
    margin-bottom: 20px;
  }
}

.advertisements-list {
  margin-bottom: 40px;

  @media (max-width: 768px) {
    margin-bottom: 30px;
  }

  @media (max-width: 480px) {
    margin-bottom: 25px;
  }
}

.no-items {
  text-align: center;
  padding: 60px 30px;
  background: linear-gradient(135deg, var(--light-color, #f8f9fa) 0%, var(--side_back, #e9ecef) 100%);
  border-radius: 20px;
  border: 1px solid var(--border, #dee2e6);

  p {
    font-family: 'Prata', serif;
    font-size: 1.1rem;
    color: var(--text-color, #333);
    margin: 0;
  }
}

.ads-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.ad-card {
  background: linear-gradient(135deg, var(--light-color, #f8f9fa) 0%, var(--side_back, #e9ecef) 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border, #dee2e6);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    border-color: var(--text-color, #333);

    .ad-image {
      transform: scale(1.05);
    }

    .ad-link-indicator {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @media (max-width: 768px) {
    border-radius: 18px;

    &:hover {
      transform: translateY(-4px);
    }
  }

  @media (max-width: 480px) {
    border-radius: 16px;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

.ad-image-container {
  position: relative;
  width: 100%;
  height: 220px;
  overflow: hidden;
  background: linear-gradient(135deg, var(--light-color, #f8f9fa) 0%, var(--side_back, #e9ecef) 100%);
  display: block !important; // Force display regardless of image errors

  .ad-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .ad-image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--text-color, #333);
    background: linear-gradient(135deg, var(--light-color, #f8f9fa) 0%, var(--side_back, #e9ecef) 100%);

    svg {
      opacity: 0.6;
    }
  }
}

.ad-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.ad-title {
  font-family: 'Prata', serif;
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--text-color, #333);
  margin: 0;
  line-height: 1.4;

  @media (max-width: 480px) {
    font-size: 1.1rem;
  }
}

.ad-description {
  font-family: 'Prata', serif;
  font-size: 0.95rem;
  color: var(--text-color, #333);
  margin: 0;
  line-height: 1.6;
  opacity: 0.8;
  flex: 1;

  @media (max-width: 480px) {
    font-size: 0.9rem;
  }
}

.ad-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 8px;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

.ad-date {
  font-family: 'Prata', serif;
  font-size: 0.85rem;
  color: var(--text-color, #333);
  margin: 0;
}

.ad-link-indicator {
  font-family: 'Prata', serif;
  font-size: 0.9rem;
  font-weight: 400;
  color: var(--text-color, #333);
  opacity: 0.7;
  transform: translateX(-8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  @media (max-width: 480px) {
    font-size: 0.85rem;
  }
}

// Responsive adjustments
@media (max-width: 1024px) {
  .container {
    padding: 16px;
  }

  .ads-grid {
    gap: 20px;
  }

  .ad-image-container {
    height: 200px;
  }

  .ad-content {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .ad-image-container {
    height: 180px;
  }

  .ad-content {
    padding: 18px;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px;
  }

  .ad-card {
    border-radius: 16px;
  }

  .ad-image-container {
    height: 160px;
  }

  .ad-content {
    padding: 16px;
  }

  .no-items {
    padding: 40px 20px;
    border-radius: 16px;
  }
}
