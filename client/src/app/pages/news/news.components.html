<div>
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Новости</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="advertisements-list">
        @if (advertisings.length > 0) {
        <div class="ads-grid">
          @for (ad of advertisings; track ad.id) {
          <article class="ad-card" (click)="navigateToLink(ad.link)">
            <div class="ad-image-container">
              @if (ad.image) {
              <img [ngSrc]="environment.serverUrl + '/upload/' + ad.image.name" [alt]="ad.title" width="300"
                height="200" class="ad-image" (error)="onImageError($event)" priority="false">
              } @else {
              <div class="ad-image-placeholder">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M21 19V5C21 3.9 20.1 3 19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19ZM8.5 13.5L11 16.51L14.5 12L19 18H5L8.5 13.5Z"
                    fill="currentColor" />
                </svg>
              </div>
              }
            </div>

            <div class="ad-content">
              <h3 class="ad-title">{{ ad.title }}</h3>
              <p class="ad-description">{{ ad.description }}</p>
              <div class="ad-meta">
                <time class="ad-date">{{ ad.date | date:'mediumDate' }}</time>
                <span class="ad-link-indicator">Читать далее →</span>
              </div>
            </div>
          </article>
          }
        </div>
        } @else {
        <div class="no-items">
          <p>Новости пока отсутствуют</p>
        </div>
        }
      </div>
    </div>
  </div>
</div>