.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;

  h3 {
    margin: 0;
    color: #333;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    
    &:hover {
      color: #333;
    }
  }
}

.modal-body {
  padding: 20px;
}

.content-info {
  margin-bottom: 20px;

  h4 {
    margin: 0 0 10px 0;
    color: #333;
  }

  .price-info {
    display: flex;
    gap: 15px;
    
    .price {
      display: flex;
      align-items: center;
      gap: 5px;
      
      .currency {
        font-weight: bold;
        color: #666;
      }
      
      .amount {
        font-size: 18px;
        font-weight: bold;
        color: #2c5aa0;
      }
    }
  }
}

.payment-methods {
  h5 {
    margin: 0 0 15px 0;
    color: #333;
  }
}

.payment-option {
  margin-bottom: 10px;

  label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border: 2px solid #eee;
    border-radius: 6px;
    transition: border-color 0.2s;

    &:hover {
      border-color: #2c5aa0;
    }

    input[type="radio"] {
      margin-right: 10px;
    }

    input[type="radio"]:checked + .payment-label {
      color: #2c5aa0;
    }
  }

  .payment-label {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
  }

  .payment-logo {
    height: 20px;
    width: auto;
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;

  .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;

    &.btn-primary {
      background-color: #2c5aa0;
      color: white;

      &:hover:not(:disabled) {
        background-color: #1e3f73;
      }

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }

    &.btn-secondary {
      background-color: #6c757d;
      color: white;

      &:hover {
        background-color: #545b62;
      }
    }
  }
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 10px;
  }

  .price-info {
    flex-direction: column;
    gap: 10px;
  }

  .modal-footer {
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
}
