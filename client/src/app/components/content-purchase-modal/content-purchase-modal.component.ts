import { CommonModule } from '@angular/common'
import { Component, EventEmitter, inject, Input, Output, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { ContentService } from '../../services/content.service'
import { ProfileService } from '../../services/profile.service'
import { ToasterService } from '../../services/toaster.service'

@Component({
  selector: 'app-content-purchase-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './content-purchase-modal.component.html',
  styleUrls: ['./content-purchase-modal.component.scss']
})
export class ContentPurchaseModalComponent {
  @Input() contentData = signal<any>(null);
  @Input() isVisible = signal<boolean>(false);
  @Output() close = new EventEmitter<void>();
  @Output() purchaseComplete = new EventEmitter<void>();

  paymentType = signal<string>('stripe');
  isLoading = signal<boolean>(false);

  contentService = inject(ContentService);
  profileService = inject(ProfileService);
  toasterService = inject(ToasterService);

  onClose() {
    this.close.emit();
  }

  onPurchase() {
    if (!this.profileService.profile) {
      this.toasterService.showToast('Необходимо авторизоваться', 'error', 'bottom-middle');
      return;
    }

    this.isLoading.set(true);

    this.contentService.purchase(this.contentData().id, this.paymentType()).subscribe({
      next: (res: any) => {
        localStorage.setItem('redirect', location.href);
        location.href = res.paymentUrl;
      },
      error: (err) => {
        this.isLoading.set(false);
        this.toasterService.showToast(err.error?.message || 'Ошибка при покупке', 'error', 'bottom-middle');
      }
    });
  }

  onPaymentTypeChange(type: string) {
    this.paymentType.set(type);
  }
}
