import {
  Component,
  Input,
  ContentChild,
  TemplateRef,
  signal,
  computed,
  OnInit,
  effect,
  EventEmitter,
  Output
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-scaled-carousel',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './scaled-carousel.component.html',
  styleUrl: './scaled-carousel.component.scss'
})
export class ScaledCarouselComponent<T> implements OnInit {
  @Input({ required: true }) items: T[] = [];
  @Input() itemsPerView = 5;
  @Output() activeSlideChange = new EventEmitter<T>();

  activeSlide = computed(() => {
    const currentRealIndex = this.getCurrentRealIndex();
    return this.items[currentRealIndex] || null;
  });

  @ContentChild(TemplateRef) itemTemplate!: TemplateRef<any>;

  currentIndex = signal(0);
  offset = signal(0);

  constructor() {

    effect(() => {
      const active = this.activeSlide();
      if (active) {
        this.activeSlideChange.emit(active);
      }
    });
  }

  extendedItems = computed(() => {
    if (this.items.length === 0) return [];

    let validItemsPerView = Math.max(this.itemsPerView, this.items.length);
    if (validItemsPerView % 2 === 0) {
      validItemsPerView += 1;
    }

    const result: T[] = [];
    const centerIndex = Math.floor(validItemsPerView / 2);
    const currentOffset = this.offset();

    for (let i = 0; i < validItemsPerView; i++) {
      const position = i - centerIndex + currentOffset;
      let sourceIndex: number;

      sourceIndex = ((position % this.items.length) + this.items.length) % this.items.length;
      result.push(this.items[sourceIndex]);
    }

    return result;
  });

  ngOnInit() {
    this.offset.set(0);
  }

  getItemZoom(itemIndex: number): number {
    let validItemsPerView = Math.max(this.itemsPerView, this.items.length);
    if (validItemsPerView % 2 === 0) {
      validItemsPerView += 1;
    }

    const centerIndex = Math.floor(validItemsPerView / 2);
    const relativeIndex = Math.abs(itemIndex - centerIndex);

    switch (relativeIndex) {
      case 0:
        return 1.3;
      case 1:
        return 1.0;
      default:
        return 0.75;
    }
  }

  next() {
    this.offset.update((current: number) => current + 1);
  }

  prev() {
    this.offset.update((current: number) => current - 1);
  }

  goTo(targetIndex: number) {

    this.currentIndex.set(this.items.length + targetIndex);
  }

  getRealIndex(extendedIndex: number): number {
    return extendedIndex % this.items.length;
  }

  getCurrentRealIndex(): number {
    const currentOffset = this.offset();
    return ((currentOffset % this.items.length) + this.items.length) % this.items.length;
  }
}
