@import '../../../assets/styles/new-palette';

.carousel {
  position: relative;
  overflow: hidden;
  width: 100%;

  .carousel-track-wrapper {
    overflow: hidden;
    display: flex;
    align-items: center;
    min-height: 635px;
    position: relative;
    width: 100%;
    margin: 0 auto;

    @media (max-width: 650px) {
      min-height: 540px;
    }

    @media (max-width: 520px) {
      min-height: 484px;
    }

    @media (max-width: 430px) {
      min-height: 435px;
    }
  }

  .carousel-track {
    display: flex;
    align-items: center;
    gap: 20px;
    position: absolute;
    width: fit-content;
    height: 615px;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    transition: transform 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);

    @media (max-width: 650px) {
      height: 460px;
    }

    @media (max-width: 520px) {
      height: 400px;
    }

    @media (max-width: 430px) {
      height: 350px;
    }

  }

  .carousel-item {
    flex: 0 0 auto;
    zoom: 1;
    transition: all 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
    cursor: pointer;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    transform-origin: center;
    opacity: 1;
    width: 330px;

    @media (max-width: 1110px) {
      width: 310px;
    }

    @media (max-width: 650px) {
      width: 250px;
    }

    @media (max-width: 520px) {
      width: 200px;
    }

    @media (max-width: 430px) {
      width: 150px;
    }

    &.center {
      z-index: 10;
    }

  }

  .nav {
    position: absolute;
    top: 45%;
    transform: translateY(-50%);
    background-color: #FFF6E080;
    backdrop-filter: blur(10px);
    border: 1px solid #F1B94F;
    border-radius: 50%;
    cursor: pointer;
    z-index: 20;
    width: 72px;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    transition: background-color 0.2s ease-in-out;

    &:hover {
      background-color: #f0e2c07a;
    }

    @media (max-width: 1110px) {
      width: 60px;
      height: 60px;
      top: 40%;
    }

    @media (max-width: 650px) {
      width: 50px;
      height: 50px;
    }

    @media (max-width: 430px) {
      top: 35%;
    }

    &.left {
      left: 20px;
    }

    &.right {
      right: 20px;
    }

    img {
      width: 24px;
      height: 24px;
    }
  }

  .indicators {
    text-align: center;
    margin: 40px auto 0;
    display: flex;
    justify-content: center;
    max-width: 600px;
    gap: 12px;

    @media (max-width: 1100px) {
      max-width: 500px;
    }

    @media (max-width: 768px) {
      max-width: 340px;
      gap: 8px;
    }

    .dot {
      display: inline-block;
      width: 32px;
      height: 4px;
      background: #FFE6AE;
      border-radius: 2px;
      flex: 1 1 0;
      max-width: 32px;

      cursor: pointer;

      &.active {
        background: #99601A;
      }

      @media (max-width: 768px) {
        width: 24px;
        max-width: 24px;
        height: 3px;
      }

      @media (max-width: 500px) {
        width: 12px;
        max-width: 12px;
      }
    }
  }
}