<div class="carousel">
  <!-- Лента -->
  <div class="carousel-track-wrapper">
    <div 
      class="carousel-track" 
    >
    
      <ng-container *ngFor="let item of extendedItems(); let i = index">
        <div 
          class="carousel-item" 
          [style.zoom]="getItemZoom(i)"
          (click)="goTo(getRealIndex(i))"
          >
          <ng-container
            *ngTemplateOutlet="itemTemplate; context: { $implicit: item }"
          ></ng-container>
        </div>
      </ng-container>
    </div>
  </div>

  <!-- Кнопки -->
  <button class="nav left" (click)="prev()">
    <img src="../../../assets/images/main-v2/arrow-left.svg" alt="left arrow">
  </button>
  <button class="nav right" (click)="next()">
    <img src="../../../assets/images/main-v2/arrow-right.svg" alt="right arrow">
  </button>

    <!-- Индикаторы -->
  <div class="indicators">
    <span
      *ngFor="let i of extendedItems(); let idx = index"
      class="dot"
      [class.active]="idx === getCurrentRealIndex()"
      (click)="goTo(idx)"
    ></span>
  </div>
</div>