@import '../../../../assets/styles/new-palette';
@import '../../../../assets/styles/new-typography';

.join-card {
    display: flex;
    flex-direction: column;
    padding: 1px;
    background-color: main(50);
    max-width: 210px;
    transition: all 0.2s ease-in-out;
    padding: 1px;
    width: 100%;
    cursor: pointer;

    @media (max-width: 1100px) {
        max-width: 100px;
    }

    @media (max-width: 650px) {
        max-width: 350px;
        margin: 0 auto;
    }
    
    &:hover {
        max-width: 280px;
        
        @media (max-width: 1100px) {
            max-width: 155px;
        }
        
        @media (max-width: 650px) {
            max-width: 350px;
            margin: 0 auto;
        }

        .card-bg {
            .background-mask-left {
                height: 155px;
                background: url(../../../../assets/images/main-v2/join-card-mask-left-hover.webp) no-repeat top left;

                @media (max-width: 1100px) {
                    height: 95px;
                }

                left: 0;
                background-size: cover;
            }

            .background-mask-right {
                height: 155px;
                background: url(../../../../assets/images/main-v2/join-card-mask-right-hover.webp) no-repeat top right;

                @media (max-width: 1100px) {
                    height: 95px;
                }

                right: 0;
                background-size: cover;
            }
        }
    }

    // padding: 22px 0;
    .card-bg {
        position: relative;
        overflow: hidden;
        height: fit-content;
        width: 100%;

        .background-mask-mobile {
            background: url(../../../../assets/images/main-v2/join-card-mask-mobile.webp) no-repeat top left;
            background-size: cover;
            position: absolute;
            z-index: 3;
            top: 0;
            left: 0;
            width: 100%;
            height: 72px;
        }

        .background-mask-left {
            position: absolute;
            transition: all 0.2s ease-in-out;
            width: 50%;
            top: 0;
            height: 159px;
            background: url(../../../../assets/images/main-v2/join-card-mask-left.webp) no-repeat top left;
            background-size: cover;
            z-index: 3;
            left: 0;

            @media (max-width: 1100px) {
                height: 100px;
            }
            @media (max-width: 650px) {
                display: none;
            }
        }

        .background-mask-right {
            position: absolute;
            height: 159px;
            width: 50%;
            top: 0;
            background: url(../../../../assets/images/main-v2/join-card-mask-right.webp) no-repeat top right;
            background-size: cover;
            z-index: 3;
            transition: all 0.2s ease-in-out;
            right: 0;

            @media (max-width: 1100px) {
                height: 100px;
            }

            @media (max-width: 650px) {
                display: none;
            }
        }

        .card-img {
            border-radius: 110px 110px 6px 6px;
            height: auto;
            width: 100%;
            height: 100%;
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
            z-index: 1;
            height: 510px;

            @media (max-width: 1100px) {
                height: 264px;
            }

            @media (max-width: 650px) {
                height: 72px;
                border-radius: 6px;
            }
        }
    }
    
    .card-date {
        color: main(700);
        margin: 32px 0 0;
        @include caption-2;
        line-height: 100%;
        letter-spacing: 0;
        vertical-align: middle;
        @media (max-width: 1100px) {
            margin: 16px 0 0;
            @include caption-5;
        }
        @media (max-width: 650px) {
            display: none;
        }
    }
    
    .card-title {
        @include subtitle-1;
        color: main(600);
        margin-top: 24px;
        @media (max-width: 1100px) {
            margin-top: 16px;
            @include body-3;
        }
        @media (max-width: 650px) {
            @include subtitle-4;
            text-align: center;
        }
    }
    
    .card-description {
        color: main(700);
        @include body-1;
        margin-top: 24px;
        @media (max-width: 1100px) {
            margin-top: 16px;
            @include caption-3;
        }
        @media (max-width: 650px) {
            display: none;
        }
    }
}