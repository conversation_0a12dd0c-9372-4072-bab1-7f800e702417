@import '../../../../assets/styles/new-palette';
@import '../../../../assets/styles/new-typography';

.card-with-domes {
    display: flex;
    flex-direction: column;
    padding: 1px;
    background-color: main(50);
    cursor: pointer;

    &:hover {
        .hover-mask {
            opacity: 1;
            z-index: 2;
        }

        .secondary-btn {
            opacity: 1;
            z-index: 5;
        }
    }

    // padding: 22px 0;
    .card-head {
        position: relative;
        overflow: hidden;
        height: fit-content;
        width: 100%;

        .background-mask-img {
            z-index: 3;
            position: sticky;
        }

        .card-img {
            position: absolute;
            border-radius: 6px;
            width: 98%;
            height: 98%;
            object-fit: cover;
            object-position: center;
            top: 1px;
            left: 1px;
            z-index: 1;
            transform: translate(1%, 1%);
        }
    }

    .card-footer {
        // background-image: url('../../../../assets/images/main-v2/card-with-domes-mask-footer.webp');
        background-color: main(100);
        border: 1px solid #fbe7b9;
        border-radius: 0 0 8px 8px;
        border-top: none;
        // height: 300px;
        background-repeat: no-repeat;
        padding: 0 7% 7%;

        .card-date {
            color: main(500);
            margin: 8px 0;
            @include caption-2;
            line-height: 100%;
            letter-spacing: 0;
            vertical-align: middle;
            text-align: end;
            @media (max-width: 1110px) {
                @include caption-4;
            }
            @media (max-width: 650px) {
                @include caption-5;
            }
        }

        .card-baige {
            color: main(400);
            padding: 8px;
            @include caption-1;
            letter-spacing: 0;
            vertical-align: middle;
            border: 1px solid main(300);
            border-radius: 6px;
            @media (max-width: 1110px) {
                @include caption-4;
                padding: 6px;
                border-radius: 4px;
            }
            @media (max-width: 650px) {
                @include caption-5;
            }
        }
        
        .card-title {
            @include subtitle-1;
            color: main(800);
            margin: 30px 0 24px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            @media (max-width: 1110px) {
                @include subtitle-4;
                margin: 20px 0 16px;
            }
            @media (max-width: 650px) {
                @include body-2;
                margin: 16px 0;
            }
        }
    }
    
    .card-description {
        color: main(500);
        @include body-1;
        display: -webkit-box;
        -webkit-line-clamp: 7;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        @media (max-width: 1110px) {
            @include body-3;
        }
    }
    
    .hover-mask {
        width: calc(100% - 3px);
        height: 97%;
        position: absolute;
        border-radius: 6px;
        top: 1px;
        left: 1px;
        background: rgba(255, 255, 255, 0.021);
        opacity: 0;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .secondary-btn {
        top: 55%;
        left: 50%;
        position: absolute;
        transform: translateX(-50%);
        opacity: 0;
        background: url(../../../../assets/images/main-v2/secondary-button_l.webp) no-repeat center;
        background-size: cover;
        cursor: pointer;
        width: 138px;
        height: 33px;
        display: flex;
        align-items: center;
        justify-content: center;
        @include button-2;
        color: main(600);
        transition: all 0.2s ease-in-out;
        &:hover {
            background: url(../../../../assets/images/main-v2/secondary-button_md-hover.webp) no-repeat center;
            background-size: cover;
            width: 138px;
            height: 34px;
        }
        @media (max-width: 1110px) {
            width: 101px;
            height: 24px;
            @include button-5;
            &:hover {
                width: 103px;
                height: 25px;
            }
        }
        @media (max-width: 430px) {
            width: 77px;
            height: 18px;
            font-size: 9px;
            line-height: 12px;
            &:hover {
                width: 77px;
                height: 19px;
            }

        }
    }
}