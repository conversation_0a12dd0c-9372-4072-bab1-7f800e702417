@import '../../../../assets/styles/new-palette';
@import '../../../../assets/styles/new-typography';

.accent-card {
    display: flex;
    flex-direction: column;
    padding: 1px;
    background-color: main(50);
    transition: all 0.2s ease-in-out;
    &:hover {
        .hover-mask {
            opacity: 1;
            z-index: 2;
        }
        .secondary-btn {
            opacity: 1;
            z-index: 5;
        }
    }

    // padding: 22px 0;
    .card-bg {
        position: relative;
        overflow: hidden;
        height: fit-content;
        width: 100%;
        .background-mask-img {
            z-index: 3;
            position: sticky;
        }
        .card-img {
            position: absolute;
            border-radius: 6px;
            width: 98%;
            height: 98%;
            object-fit: cover;
            object-position: center;
            top: 1px;
            left: 1px;
            z-index: 1;
            transform: translate(1%, 1%);
        }
    }

    .accent-card-title-section {
        padding: 0 22px 22px;
        background-color: main(100);
        margin-bottom: 1rem;
        @media (max-width: 1110px) {
            border-radius: 0 0 6px 6px;
        }
        @media (max-width: 650px) {
            padding: 0 18px 18px;
        }
        @media (max-width: 520px) {
            padding: 0 12px 12px;
        }
        .date-level-box {
            @media (max-width: 650px) {
                flex-direction: column-reverse;
                align-items: flex-start;
                
            }
        }
        .card-date {
            color: main(600);
            margin: 8px 0;
            @include caption-3;
            line-height: 100%;
            letter-spacing: 0;
            vertical-align: middle;
            text-align: end;
            @media (max-width: 1110px) {
                @include caption-4;
            }
            @media (max-width: 650px) {
                margin: 6px 0;
                @include caption-5;
            }
        }
        .card-level {
            color: main(600);
            margin: 8px 0;
            @include caption-3;
            line-height: 100%;
            letter-spacing: 0;
            vertical-align: middle;
            @media (max-width: 1110px) {
                @include caption-4;
            }
            @media (max-width: 650px) {
                margin: 6px 0;
            }
        }
        
        .card-title {
            @include body-2;
            color: main(700);
            margin-top: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            @media (max-width: 1110px) {
                @include body-3;
            }
            @media (max-width: 650px) {
                @include button-4;
            }
        }
    }
    
    .card-description {
        color: main(500);
        @include body-3;
        padding: 0 22px;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        @media (max-width: 1110px) {
            @include button-5;
        }
        @media (max-width: 650px) {
            color: main(700);
            position: absolute;
            top: 100%;
            width: 430px;
            max-width: 80vw;
            transform: translateX(-50%);
            left: 50%;
            padding: 0 22px;
            hyphens: auto;
        }
    }

    .hover-mask {
        width: 95%;
        height: 96%;
        position: absolute;
        border-radius: 6px;
        top: 1px;
        left: 2px;
        background: rgba(255, 255, 255, 0.01);
        opacity: 0;
        transition: all 0.3s ease;
        backdrop-filter: blur(7px);

    }
    .secondary-btn {
        top: 53%;
        left: 50%;
        position: absolute;
        transform: translateX(-50%);
        opacity: 0;
        background: url(../../../../assets/images/main-v2/secondary-button_l.webp) no-repeat center;
        background-size: cover;
        cursor: pointer;
        width: 138px;
        height: 33px;
        display: flex;
        align-items: center;
        justify-content: center;
        @include button-2;
        color: main(600);
        transition: all 0.2s ease-in-out;
        &:hover {
            background: url(../../../../assets/images/main-v2/secondary-button_md-hover.webp) no-repeat center;
            background-size: cover;
            width: 138px;
            height: 34px;
        }
        @media (max-width: 1110px) {
            width: 101px;
            height: 24px;
            @include button-5;
            &:hover {
                width: 103px;
                height: 25px;
            }
        }
        @media (max-width: 430px) {
            width: 77px;
            height: 18px;
            font-size: 9px;
            line-height: 12px;
            &:hover {
                width: 77px;
                height: 19px;
            }

        }
    }
}