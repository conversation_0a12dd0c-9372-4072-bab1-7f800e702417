@import '../../../assets/styles/new-palette';
@import '../../../assets/styles/new-typography';

footer {
    background-image: url(../../../assets/images/main-v2/Footer.webp);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    height: 846px;
    position: relative;
    padding: 107px 34px 36px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    @include caption-2;
    color: main(100);

    @media (max-width: 1250px) {
        padding: 42px 34px 36px;
    }

    @media (max-width: 768px) {
        padding: 36px 34px 36px;
    }

    @media (max-width: 650px) {
        padding: 60px 16px 36px;
        gap: 40px;
    }

    @media (max-width: 500px) {
        padding: 60px 16px 100px;
        gap: 40px;
    }

    .up-btn {
        background: url(../../../assets/images/main-v2/Up_Button.webp) no-repeat center;
        background-size: contain;
        width: 72px;
        height: 72px;
        cursor: pointer;
        position: absolute;
        bottom: 26px;
        right: 15px;
        z-index: 1000;
        transition: all 0.2s ease-in-out;

        @media (max-width: 768px) {
            width: 40px;
            height: 40px;
        }

        @media (max-width: 500px) {
            display: none;
        }

        &:hover {
            opacity: 0.8;
        }
    }

    .logo {
        width: 62px;
        height: 62px;
        max-width: 100%;

        @media (max-width: 430px) {
            width: 48px;
            height: 48px;
        }
    }

    .footer-head-mask {
        width: 100%;
        height: 120px;
        background: linear-gradient(180deg, rgba(255, 235, 191, 0) 0%, #FFEBBF 100%);
        background-image: url(assets/images/main-v2/footer-mask-xl.webp);
        background-position: top;
        background-size: cover;
        background-repeat: no-repeat;
        position: absolute;
        left: 0;
        top: -1px;
        z-index: 2;

        @media (max-width: 1500px) {
            background-image: url(../../../assets/images/main-v2/footer-mask-lg.webp);
        }

        @media (max-width: 920px) {
            background-image: url(../../../assets/images/main-v2/footer-mask-md.webp);
        }

        @media (max-width: 560px) {
            background-image: url(../../../assets/images/main-v2/footer-mask-sm.webp);
        }
    }

    .desktop-logo {
        position: absolute;
        width: 120px;
        height: 120px;
        top: 34px;
        left: 50%;
        transform: translateX(-50%);

        @media (max-width: 1250px) {
            display: none;
        }
    }

    .desktop-title {
        display: block;
        color: main(100);
        max-width: 450px;

        .title {
            @include button-1;
            margin-bottom: 32px;
        }

        .subtitle {
            @include body-3;
        }

        @media (max-width: 1250px) {
            display: none;
        }
    }

    .mobile-title {
        display: none;
        color: main(700);
        max-width: 750px;
        margin: 0 auto;

        @media (max-width: 1250px) {
            display: block;
        }

        @media (max-width: 768px) {
            max-width: 580px;
        }

        @media (max-width: 430px) {
            color: main(600);
            max-width: 344px;
        }

        .title {
            @include subtitle-1;
            margin: 32px 0;
            text-align: center;

            @media (max-width: 430px) {
                margin: 32px auto 24px;
                @include subtitle-4;
                max-width: 234px;
            }
        }

        .subtitle {
            @include body-3;
            text-align: center;
        }

        .shared-mobile-section {
            display: none;

            .label {
                @include body-2;
                color: main(50);
            }

            .mail {
                @include body-2;
                color: main(100);
                cursor: pointer;
            }

            @media (max-width: 650px) {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin-top: 42px;
                text-align: center;
                gap: 16px;
            }
        }
    }

    .label {
        @include body-2;
        color: main(300);

        @media (max-width: 768px) {
            @include body-3;
        }
    }

    .privacy-policy {
        @include button-4;
        color: main(100);

        @media (max-width: 768px) {
            @include caption-3;
        }

        &-link {
            cursor: pointer;
            transition: all 0.2s ease-in-out;

            &:hover {
                color: main(300);
            }

            &:active {
                color: main(200);
            }

            @media (max-width: 768px) {
                @include button-5;
            }
        }
    }

    .links-section {
        display: flex;
        gap: 50px;
        padding-right: 60px;

        @media (max-width: 768px) {
            padding-right: 40px;
        }

        @media (max-width: 650px) {
            padding-right: 0;
            flex-direction: column-reverse;
        }

        .left-content {
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .shared-section {
                display: flex;
                flex-direction: column;
                gap: 40px;
                min-width: fit-content;

                @media (max-width: 650px) {
                    display: none;
                }
            }
        }

        .right-content {
            display: flex;
            gap: 30px;

            @media (max-width: 768px) {
                padding-bottom: 35px;
            }

            .links-col {
                display: flex;
                flex-direction: column;
                gap: 30px;

                @media (max-width: 768px) {
                    gap: 20px;
                }

                @media (max-width: 650px) {
                    flex: 1 1 0;
                }

                a {
                    @include button-4;
                    cursor: pointer;
                    transition: all 0.2s ease-in-out;

                    @media (max-width: 768px) {
                        @include button-5;
                    }

                    &:hover {
                        color: main(300);
                    }

                    &:active {
                        color: main(200);
                    }
                }
            }
        }
    }

    .icons {
        color: main(100);
        width: 248px;
        @media (max-width: 650px) {
            width: 300px;
        }
        svg {
            &:hover {
                path {
                    fill: main(300);
                }
            }

            &:active {
                path {
                    fill: main(400);
                }
            }

            path {
                transition: all 0.2s ease-in-out;
                fill: currentColor;
            }
        }
    }

    .mail {
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        color: main(100);
        @include body-2;
        transition: all 0.2s ease-in-out;
        height: 24px;
        svg {
            width: 0;
            opacity: 0;
            transition: all 0.2s ease-in-out;
            @media (max-width: 650px) {
                zoom: 0.8;
            }
        }
        span {
            transition: all 0.2s ease-in-out;
        }
        &:hover {
            color: main(200);
            svg {
                transition: all 0.2s ease-in-out;
                width: 24px;
                opacity: 1;
            }
        }
    }
}