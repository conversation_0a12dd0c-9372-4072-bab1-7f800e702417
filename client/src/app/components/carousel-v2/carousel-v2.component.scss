.carousel {
  position: relative;
  overflow: hidden;
  width: 100%;

  .carousel-track-wrapper {
    overflow: hidden;
  }

  .carousel-track {
    display: flex;
    transition: transform 0.4s ease-in-out;
    width: 100%;
  }

  .carousel-item {
    // flex: 0 0 calc(100% / var(--items-per-view, 3));
    box-sizing: border-box;
    padding: 0 15px;

    @media (max-width: 1110px) {
      padding: 0 10px;
    }
    
    @media (max-width: 650px) {
      padding: 0 4px;
    }
  }

  .nav {
    position: absolute;
    top: 40%;
    transform: translateY(-50%);
    background: #fff;
    border: none;
    cursor: pointer;
    font-size: 2rem;
    z-index: 2;

    &.left {
      width: 72px;
      height: 72px;
      left: 0.5rem;
      border: 1px solid;
      border-radius: 50%;
      background-color: #FFF6E080;
      backdrop-filter: blur(10px);
      border: 1px solid #F1B94F;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s ease-in-out;
      &:hover {
        background-color: #f0e2c07a;
      }
      // background: url(assets/images/main-v2/direction-left.webp);
      @media (max-width: 1110px) {
        width: 60px;
        height: 60px;
      }
      
      @media (max-width: 650px) {
        width: 50px;
        height: 50px;
      }
      @media (max-width: 430px) {
        top: 30%;
      }
    }

    &.right {
      width: 72px;
      height: 72px;
      border-radius: 50%;
      // background: url(assets/images/main-v2/direction-right.webp);
      border: 1px solid;
      background-color: #FFF6E080;
      backdrop-filter: blur(10px);
      border: 1px solid #F1B94F;
      right: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s ease-in-out;
      &:hover {
        background-color: #f0e2c07a;
      }
      @media (max-width: 1110px) {
        width: 60px;
        height: 60px;
      }
      
      @media (max-width: 650px) {
        width: 50px;
        height: 50px;
      }
      @media (max-width: 430px) {
        top: 30%;
      }
    }

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }
  }

  .indicators {
   text-align: center;
    margin: 56px auto 0;
    display: flex;
    justify-content: center;
    max-width: 600px;
    gap: 12px;
    @media (max-width: 1100px) {
      max-width: 500px;
    }
    @media (max-width: 768px) {
      margin: 40px auto 0;
      max-width: 340px;
      gap: 8px;
    }

    .dot {
      display: inline-block;
      width: 32px;
      height: 4px;
      background: #FFE6AE;
      border-radius: 2px;
      flex: 1 1 0;
      max-width: 32px;

      cursor: pointer;

      &.active {
        background: #99601A;
      }

      @media (max-width: 768px) {
        width: 24px;
        max-width: 24px;
        height: 3px;
      }
      @media (max-width: 500px) {
        width: 12px;
        max-width: 12px;
      }
    }
  }
}