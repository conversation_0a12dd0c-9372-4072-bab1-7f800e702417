import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

export interface ActiveFilter {
  id: string | number;
  name: string;
  type: 'tag' | 'author' | 'format' | 'year' | 'paid' | 'category';
  value?: any;
}

@Component({
  selector: 'app-active-filters',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="active-filters-wrap" *ngIf="filters.length > 0">
      <div class="filter-item-wrapper" *ngFor="let filter of filters; trackBy: trackByFilter">
        <span class="filter-item">
          {{filter.name}}
          <span class="filter-remove" (click)="removeFilter(filter)"></span>
        </span>
      </div>
    </div>
  `,
  styleUrls: ['./active-filters.component.scss']
})
export class ActiveFiltersComponent {
  @Input() filters: ActiveFilter[] = [];
  @Output() filterRemoved = new EventEmitter<ActiveFilter>();

  removeFilter(filter: ActiveFilter) {
    this.filterRemoved.emit(filter);
  }

  trackByFilter(index: number, filter: ActiveFilter): any {
    return filter.id + '_' + filter.type;
  }
}
