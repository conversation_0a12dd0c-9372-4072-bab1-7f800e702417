[{"id": 15, "code": "content.news.published", "translations": [{"lang": "ru", "text": "Опубликована новость"}, {"lang": "en", "text": "News published"}, {"lang": "de", "text": "Nachricht verö<PERSON>ntlicht"}, {"lang": "ua", "text": "Новину опубліковано"}, {"lang": "it", "text": "Notizia pubblicata"}]}, {"id": 16, "code": "forum.topic.created_activity", "translations": [{"lang": "ru", "text": "В вашей теме появилось новое сообщение"}, {"lang": "en", "text": "A new message has appeared in your topic"}, {"lang": "de", "text": "Eine neue Nachricht ist in Ihrem Thema erschienen"}, {"lang": "ua", "text": "У вашій темі з'явилося нове повідомлення"}, {"lang": "it", "text": "È apparso un nuovo messaggio nel tuo argomento"}]}, {"id": 17, "code": "forum.post.reply", "translations": [{"lang": "ru", "text": "Кто-то ответил на ваше сообщение"}, {"lang": "en", "text": "Someone replied to your message"}, {"lang": "de", "text": "Je<PERSON> hat auf Ihre Nachricht geantwortet"}, {"lang": "ua", "text": "Хтось відповів на ваше повідомлення"}, {"lang": "it", "text": "Qualcuno ha risposto al tuo messaggio"}]}, {"id": 18, "code": "forum.post.like", "translations": [{"lang": "ru", "text": "Кто-то поставил лайк вашему сообщению"}, {"lang": "en", "text": "Someone liked your message"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON> hat Ihre Nachricht gefallen"}, {"lang": "ua", "text": "Хтось вподобав ваше повідомлення"}, {"lang": "it", "text": "A qualcuno è piaciuto il tuo messaggio"}]}, {"id": 19, "code": "forum.post.favorited", "translations": [{"lang": "ru", "text": "Кто-то добавил ваше сообщение в избранное"}, {"lang": "en", "text": "Someone added your message to favorites"}, {"lang": "de", "text": "Je<PERSON> hat Ihre Nachricht zu den Favoriten hinzugefügt"}, {"lang": "ua", "text": "Хтось додав ваше повідомлення до обраного"}, {"lang": "it", "text": "Qualcuno ha aggiunto il tuo messaggio ai preferiti"}]}, {"id": 20, "code": "header.nav.main", "translations": [{"lang": "ru", "text": "Главная"}, {"lang": "en", "text": "Home"}, {"lang": "de", "text": "Startseite"}, {"lang": "ua", "text": "Головна"}, {"lang": "it", "text": "Home"}]}, {"id": 21, "code": "header.nav.forum", "translations": [{"lang": "ru", "text": "Форум"}, {"lang": "en", "text": "Forum"}, {"lang": "de", "text": "Forum"}, {"lang": "ua", "text": "Форум"}, {"lang": "it", "text": "Forum"}]}, {"id": 22, "code": "header.nav.support", "translations": [{"lang": "ru", "text": "Поддержать"}, {"lang": "en", "text": "Support"}, {"lang": "de", "text": "Unterstützen"}, {"lang": "ua", "text": "Підтримати"}, {"lang": "it", "text": "Supporta"}]}, {"id": 23, "code": "header.menu.title", "translations": [{"lang": "ru", "text": "<PERSON>е<PERSON><PERSON>"}, {"lang": "en", "text": "<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "<PERSON>е<PERSON><PERSON>"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 24, "code": "header.menu.show_more", "translations": [{"lang": "ru", "text": "Смотреть больше"}, {"lang": "en", "text": "Show more"}, {"lang": "de", "text": "<PERSON><PERSON> anzeigen"}, {"lang": "ua", "text": "Показати більше"}, {"lang": "it", "text": "Mostra di più"}]}, {"id": 25, "code": "header.menu.close", "translations": [{"lang": "ru", "text": "Закрыть меню"}, {"lang": "en", "text": "Close menu"}, {"lang": "de", "text": "<PERSON><PERSON> sch<PERSON>ßen"}, {"lang": "ua", "text": "Закрити меню"}, {"lang": "it", "text": "<PERSON>udi menu"}]}, {"id": 26, "code": "header.menu.open", "translations": [{"lang": "ru", "text": "Открыть меню"}, {"lang": "en", "text": "Open menu"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Відкрити меню"}, {"lang": "it", "text": "Apri menu"}]}, {"id": 27, "code": "header.dropdown.tradition.sanatana_dharma", "translations": [{"lang": "ru", "text": "Санатана Драхма"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON> Dharma"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON> Dharma"}, {"lang": "ua", "text": "Сана<PERSON><PERSON><PERSON> Дхарма"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON> Dharma"}]}, {"id": 28, "code": "header.dropdown.tradition.lineage", "translations": [{"lang": "ru", "text": "Линия передачи"}, {"lang": "en", "text": "Lineage"}, {"lang": "de", "text": "Übertragungslinie"}, {"lang": "ua", "text": "Лінія передачі"}, {"lang": "it", "text": "Lignaggio"}]}, {"id": 29, "code": "header.dropdown.tradition.guru", "translations": [{"lang": "ru", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "en", "text": "<PERSON>"}, {"lang": "de", "text": "<PERSON>"}, {"lang": "ua", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "it", "text": "<PERSON>"}]}, {"id": 30, "code": "header.dropdown.tradition.creed", "translations": [{"lang": "ru", "text": "Символ веры"}, {"lang": "en", "text": "<PERSON>"}, {"lang": "de", "text": "Glaubensbekenntnis"}, {"lang": "ua", "text": "Символ віри"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 31, "code": "header.dropdown.education.courses", "translations": [{"lang": "ru", "text": "Курсы"}, {"lang": "en", "text": "Courses"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Кур<PERSON>и"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 32, "code": "header.dropdown.education.basic_course", "translations": [{"lang": "ru", "text": "Базовый курс"}, {"lang": "en", "text": "Basic course"}, {"lang": "de", "text": "Grundku<PERSON>"}, {"lang": "ua", "text": "Базовий курс"}, {"lang": "it", "text": "Corso base"}]}, {"id": 33, "code": "header.dropdown.education.institute", "translations": [{"lang": "ru", "text": "Институт Васиштхи"}, {"lang": "en", "text": "Vasistha Institute"}, {"lang": "de", "text": "Vasistha-Institut"}, {"lang": "ua", "text": "Інститут Васіштхи"}, {"lang": "it", "text": "Istituto Vasistha"}]}, {"id": 34, "code": "header.dropdown.education.system", "translations": [{"lang": "ru", "text": "Система обучения"}, {"lang": "en", "text": "Education system"}, {"lang": "de", "text": "Bildungssystem"}, {"lang": "ua", "text": "Система навчання"}, {"lang": "it", "text": "Sistema educativo"}]}, {"id": 35, "code": "header.dropdown.practice.yoga_types", "translations": [{"lang": "ru", "text": "Джняна-йога, Бхакти-йога и т.д"}, {"lang": "en", "text": "Jnana-yoga, Bhakti-yoga, etc."}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>-Yoga, Bhakti-Yoga usw."}, {"lang": "ua", "text": "Джняна-йога, Бхакті-йога тощо"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>-yoga, Bhakti-yoga, ecc."}]}, {"id": 36, "code": "header.dropdown.practice.retreats", "translations": [{"lang": "ru", "text": "Ретриты"}, {"lang": "en", "text": "Retreats"}, {"lang": "de", "text": "Retreats"}, {"lang": "ua", "text": "Ретрити"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 37, "code": "header.dropdown.practice.mantra_institute", "translations": [{"lang": "ru", "text": "Институт Мантры и ритуалы Васиштхи"}, {"lang": "en", "text": "Mantra Institute and Vasistha rituals"}, {"lang": "de", "text": "Mantra-Institut und Vasistha-Rituale"}, {"lang": "ua", "text": "Інститут Мантри та ритуали Васіштхи"}, {"lang": "it", "text": "Istituto di Mantra e rituali di Vasistha"}]}, {"id": 38, "code": "header.dropdown.practice.become_student", "translations": [{"lang": "ru", "text": "Как стать учеником?"}, {"lang": "en", "text": "How to become a student?"}, {"lang": "de", "text": "Wie wird man Student?"}, {"lang": "ua", "text": "Як стати учнем?"}, {"lang": "it", "text": "Come diventare uno studente?"}]}, {"id": 39, "code": "header.dropdown.library.photo_gallery", "translations": [{"lang": "ru", "text": "Фотогалерея"}, {"lang": "en", "text": "Photo gallery"}, {"lang": "de", "text": "Fotogalerie"}, {"lang": "ua", "text": "Фотогалерея"}, {"lang": "it", "text": "Galleria fotografica"}]}, {"id": 40, "code": "header.dropdown.library.radio", "translations": [{"lang": "ru", "text": "Радио"}, {"lang": "en", "text": "Radio"}, {"lang": "de", "text": "Radio"}, {"lang": "ua", "text": "Радіо"}, {"lang": "it", "text": "Radio"}]}, {"id": 41, "code": "header.dropdown.events.calendar", "translations": [{"lang": "ru", "text": "Календарь событий"}, {"lang": "en", "text": "Events calendar"}, {"lang": "de", "text": "Veranstaltungskalender"}, {"lang": "ua", "text": "Календар подій"}, {"lang": "it", "text": "Calendario eventi"}]}, {"id": 42, "code": "header.dropdown.events.seminars", "translations": [{"lang": "ru", "text": "Семинары"}, {"lang": "en", "text": "Seminars"}, {"lang": "de", "text": "Seminare"}, {"lang": "ua", "text": "Семінари"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 43, "code": "header.burger.tradition.title", "translations": [{"lang": "ru", "text": "O Традиции"}, {"lang": "en", "text": "About Tradition"}, {"lang": "de", "text": "Über die Tradition"}, {"lang": "ua", "text": "Про Традицію"}, {"lang": "it", "text": "Sulla Tradizione"}]}, {"id": 44, "code": "header.burger.start_path.title", "translations": [{"lang": "ru", "text": "Начать путь"}, {"lang": "en", "text": "Start the Path"}, {"lang": "de", "text": "Den Weg beginnen"}, {"lang": "ua", "text": "Почати шлях"}, {"lang": "it", "text": "Iniziare il Sentiero"}]}, {"id": 45, "code": "header.burger.philosophy.title", "translations": [{"lang": "ru", "text": "Философия"}, {"lang": "en", "text": "Philosophy"}, {"lang": "de", "text": "Philosophie"}, {"lang": "ua", "text": "Філософія"}, {"lang": "it", "text": "Filosofia"}]}, {"id": 46, "code": "header.burger.education.title", "translations": [{"lang": "ru", "text": "Обучение"}, {"lang": "en", "text": "Education"}, {"lang": "de", "text": "Bildung"}, {"lang": "ua", "text": "Навчання"}, {"lang": "it", "text": "Formazione"}]}, {"id": 47, "code": "header.burger.community.title", "translations": [{"lang": "ru", "text": "Община"}, {"lang": "en", "text": "Community"}, {"lang": "de", "text": "Gemeinschaft"}, {"lang": "ua", "text": "Спільнота"}, {"lang": "it", "text": "Comunità"}]}, {"id": 48, "code": "header.burger.events.title", "translations": [{"lang": "ru", "text": "События"}, {"lang": "en", "text": "Events"}, {"lang": "de", "text": "Veranstaltungen"}, {"lang": "ua", "text": "Події"}, {"lang": "it", "text": "Eventi"}]}, {"id": 49, "code": "header.burger.media.title", "translations": [{"lang": "ru", "text": "Медиа"}, {"lang": "en", "text": "Media"}, {"lang": "de", "text": "Medien"}, {"lang": "ua", "text": "Медіа"}, {"lang": "it", "text": "Media"}]}, {"id": 50, "code": "header.burger.practice.title", "translations": [{"lang": "ru", "text": "Практика"}, {"lang": "en", "text": "Practice"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Практика"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 51, "code": "header.burger.support.title", "translations": [{"lang": "ru", "text": "Поддержать"}, {"lang": "en", "text": "Support"}, {"lang": "de", "text": "Unterstützen"}, {"lang": "ua", "text": "Підтримати"}, {"lang": "it", "text": "Supporto"}]}, {"id": 52, "code": "header.burger.forum.title", "translations": [{"lang": "ru", "text": "Форум"}, {"lang": "en", "text": "Forum"}, {"lang": "de", "text": "Forum"}, {"lang": "ua", "text": "Форум"}, {"lang": "it", "text": "Forum"}]}, {"id": 53, "code": "header.burger.tradition.foundation.title", "translations": [{"lang": "ru", "text": "Основа"}, {"lang": "en", "text": "Foundation"}, {"lang": "de", "text": "Grundlage"}, {"lang": "ua", "text": "Основа"}, {"lang": "it", "text": "Fondamento"}]}, {"id": 54, "code": "header.burger.tradition.foundation.sanatana_dharma", "translations": [{"lang": "ru", "text": "Сана<PERSON><PERSON><PERSON> Дхарма"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON> Dharma"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON> Dharma"}, {"lang": "ua", "text": "Сана<PERSON><PERSON><PERSON> Дхарма"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON> Dharma"}]}, {"id": 55, "code": "header.burger.tradition.foundation.creed", "translations": [{"lang": "ru", "text": "Символ веры"}, {"lang": "en", "text": "<PERSON>"}, {"lang": "de", "text": "Glaubensbekenntnis"}, {"lang": "ua", "text": "Символ віри"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 56, "code": "header.burger.tradition.foundation.lineage", "translations": [{"lang": "ru", "text": "Линия передачи"}, {"lang": "en", "text": "Lineage"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Лінія передачі"}, {"lang": "it", "text": "Lignaggio"}]}, {"id": 57, "code": "header.burger.tradition.foundation.paramguru", "translations": [{"lang": "ru", "text": "Пара<PERSON>гуру"}, {"lang": "en", "text": "Paramguru"}, {"lang": "de", "text": "Paramguru"}, {"lang": "ua", "text": "Пара<PERSON>гуру"}, {"lang": "it", "text": "Paramguru"}]}, {"id": 58, "code": "header.burger.tradition.foundation.juna_akhara", "translations": [{"lang": "ru", "text": "<PERSON>р<PERSON><PERSON><PERSON> Джуна Акхара"}, {"lang": "en", "text": "Juna Akhara Order"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "<PERSON>р<PERSON><PERSON><PERSON> Джуна Акхара"}, {"lang": "it", "text": "Ordine Jun<PERSON>"}]}, {"id": 59, "code": "header.burger.tradition.guru_divine.title", "translations": [{"lang": "ru", "text": "Гуру и Божественное"}, {"lang": "en", "text": "<PERSON> and the Divine"}, {"lang": "de", "text": "<PERSON> und das Göttliche"}, {"lang": "ua", "text": "Гуру і Божественне"}, {"lang": "it", "text": "Guru e il Divino"}]}, {"id": 60, "code": "header.burger.tradition.vows_path.title", "translations": [{"lang": "ru", "text": "Обеты и путь"}, {"lang": "en", "text": "Vows and Path"}, {"lang": "de", "text": "Gelübde und Weg"}, {"lang": "ua", "text": "Обіти і шлях"}, {"lang": "it", "text": "Voti e Sentiero"}]}, {"id": 61, "code": "header.burger.tradition.guru_divine.guru", "translations": [{"lang": "ru", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "en", "text": "<PERSON>"}, {"lang": "de", "text": "<PERSON>"}, {"lang": "ua", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "it", "text": "<PERSON>"}]}, {"id": 62, "code": "header.burger.tradition.guru_divine.chosen_deity", "translations": [{"lang": "ru", "text": "Избранное божество"}, {"lang": "en", "text": "<PERSON><PERSON>"}, {"lang": "de", "text": "Auserwählte Gottheit"}, {"lang": "ua", "text": "Обране божество"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 63, "code": "header.burger.tradition.guru_divine.deities", "translations": [{"lang": "ru", "text": "Божества"}, {"lang": "en", "text": "Deities"}, {"lang": "de", "text": "Gottheiten"}, {"lang": "ua", "text": "Божества"}, {"lang": "it", "text": "Divinità"}]}, {"id": 64, "code": "header.burger.tradition.guru_divine.siddhi_ethics", "translations": [{"lang": "ru", "text": "Сиддхи: понимание и этика"}, {"lang": "en", "text": "Sid<PERSON>: Understanding and Ethics"}, {"lang": "de", "text": "Siddhi: Verständnis und Ethik"}, {"lang": "ua", "text": "Сіддхі: розуміння та етика"}, {"lang": "it", "text": "Siddhi: Comprensione ed Etica"}]}, {"id": 65, "code": "header.burger.tradition.vows_path.sannyasa", "translations": [{"lang": "ru", "text": "Санньяса"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Санньяса"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 66, "code": "header.burger.tradition.vows_path.student_code", "translations": [{"lang": "ru", "text": "Кодекс ученика"}, {"lang": "en", "text": "Student Code"}, {"lang": "de", "text": "Schülerkodex"}, {"lang": "ua", "text": "Кодекс учня"}, {"lang": "it", "text": "Codice dello Studente"}]}, {"id": 67, "code": "header.burger.tradition.vows_path.community_history", "translations": [{"lang": "ru", "text": "История общины"}, {"lang": "en", "text": "Community History"}, {"lang": "de", "text": "Gemeinschaftsgeschichte"}, {"lang": "ua", "text": "Історія спільноти"}, {"lang": "it", "text": "Storia della Comunità"}]}, {"id": 68, "code": "header.burger.tradition.guru_divine.title", "translations": [{"lang": "ru", "text": "Гуру и Божественное"}, {"lang": "en", "text": "Guru and Divine"}, {"lang": "de", "text": "Guru und Göttliches"}, {"lang": "ua", "text": "Гуру і Божественне"}, {"lang": "it", "text": "Guru e Divino"}]}, {"id": 69, "code": "header.burger.tradition.featured.title", "translations": [{"lang": "ru", "text": "Избранное"}, {"lang": "en", "text": "Featured"}, {"lang": "de", "text": "Ausgewählt"}, {"lang": "ua", "text": "Обране"}, {"lang": "it", "text": "In Evidenza"}]}, {"id": 70, "code": "header.burger.tradition.featured.who_we_are", "translations": [{"lang": "ru", "text": "Кто мы и к чему ведем"}, {"lang": "en", "text": "Who We Are and What We Lead To"}, {"lang": "de", "text": "Wer wir sind und wohin wir führen"}, {"lang": "ua", "text": "Хто ми і до чого ведемо"}, {"lang": "it", "text": "Chi Siamo e Dove Conduciamo"}]}, {"id": 71, "code": "header.burger.tradition.featured.transmission_structure", "translations": [{"lang": "ru", "text": "Как устроена передача"}, {"lang": "en", "text": "How Transmission Works"}, {"lang": "de", "text": "Wie die Übertragung funktioniert"}, {"lang": "ua", "text": "Як влаштована передача"}, {"lang": "it", "text": "Come Funziona la Trasmissione"}]}, {"id": 72, "code": "header.burger.tradition.featured.ask_question", "translations": [{"lang": "ru", "text": "Задать вопрос о традиции"}, {"lang": "en", "text": "Ask a Question About Tradition"}, {"lang": "de", "text": "Eine Frage zur Tradition stellen"}, {"lang": "ua", "text": "Поставити питання про традицію"}, {"lang": "it", "text": "Fai una Domanda sulla Tradizione"}]}, {"id": 73, "code": "header.burger.start_path.title", "translations": [{"lang": "ru", "text": "Начать путь"}, {"lang": "en", "text": "Start the Path"}, {"lang": "de", "text": "Den Weg beginnen"}, {"lang": "ua", "text": "Почати шлях"}, {"lang": "it", "text": "Iniziare il Sentiero"}]}, {"id": 74, "code": "header.burger.start_path.first_steps.title", "translations": [{"lang": "ru", "text": "Первые шаги"}, {"lang": "en", "text": "First Steps"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Перші кроки"}, {"lang": "it", "text": "Primi Passi"}]}, {"id": 75, "code": "header.burger.start_path.first_steps.become_student", "translations": [{"lang": "ru", "text": "Стать учеником"}, {"lang": "en", "text": "Become a Student"}, {"lang": "de", "text": "Sc<PERSON><PERSON>ler werden"}, {"lang": "ua", "text": "Стати учнем"}, {"lang": "it", "text": "Diventare uno Studente"}]}, {"id": 76, "code": "header.burger.start_path.first_steps.take_diksha", "translations": [{"lang": "ru", "text": "Принять дикшу"}, {"lang": "en", "text": "<PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Прийняти дікшу"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 77, "code": "header.burger.start_path.first_steps.monk_consultation", "translations": [{"lang": "ru", "text": "Консультация с монахом"}, {"lang": "en", "text": "Consultation with a Monk"}, {"lang": "de", "text": "Beratung mit einem Mönch"}, {"lang": "ua", "text": "Консультація з монахом"}, {"lang": "it", "text": "Consultazione con un Monaco"}]}, {"id": 78, "code": "header.burger.start_path.community_life.title", "translations": [{"lang": "ru", "text": "Жизнь в общине"}, {"lang": "en", "text": "Community Life"}, {"lang": "de", "text": "Gemeinschaftsleben"}, {"lang": "ua", "text": "Життя в спільноті"}, {"lang": "it", "text": "Vita Comunitaria"}]}, {"id": 79, "code": "header.burger.start_path.community_life.visit_ashram", "translations": [{"lang": "ru", "text": "Приехать в ашрам"}, {"lang": "en", "text": "Visit the Ashram"}, {"lang": "de", "text": "<PERSON> besuchen"}, {"lang": "ua", "text": "Приїхати в ашрам"}, {"lang": "it", "text": "Visitare l'Ashram"}]}, {"id": 80, "code": "header.burger.start_path.community_life.perform_service", "translations": [{"lang": "ru", "text": "Выполнять служение"}, {"lang": "en", "text": "Perform Service"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Виконувати служіння"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 81, "code": "header.burger.start_path.community_life.become_monk", "translations": [{"lang": "ru", "text": "Стать монахом"}, {"lang": "en", "text": "Become a Monk"}, {"lang": "de", "text": "<PERSON><PERSON>nch werden"}, {"lang": "ua", "text": "Стати монахом"}, {"lang": "it", "text": "Diventare Monaco"}]}, {"id": 82, "code": "header.burger.start_path.practical_formats.title", "translations": [{"lang": "ru", "text": "Практические форматы"}, {"lang": "en", "text": "Practical Formats"}, {"lang": "de", "text": "Praktische Formate"}, {"lang": "ua", "text": "Практичні формати"}, {"lang": "it", "text": "Formati Pratici"}]}, {"id": 83, "code": "header.burger.start_path.practical_formats.retreat", "translations": [{"lang": "ru", "text": "Пройти ретрит"}, {"lang": "en", "text": "Attend a Retreat"}, {"lang": "de", "text": "Ein Retreat besuchen"}, {"lang": "ua", "text": "Пройти ретрит"}, {"lang": "it", "text": "Partecipare a un Ritiro"}]}, {"id": 84, "code": "header.burger.start_path.practical_formats.upcoming_events", "translations": [{"lang": "ru", "text": "Ближайшие мероприятия"}, {"lang": "en", "text": "Upcoming Events"}, {"lang": "de", "text": "Kommende Veranstaltungen"}, {"lang": "ua", "text": "Найближчі заходи"}, {"lang": "it", "text": "Eventi Prossimi"}]}, {"id": 85, "code": "header.burger.start_path.practical_formats.online_meetings", "translations": [{"lang": "ru", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-встречи для новичков"}, {"lang": "en", "text": "Online Meetings for Beginners"}, {"lang": "de", "text": "Online-Treffen für Anfänger"}, {"lang": "ua", "text": "Он<PERSON><PERSON><PERSON>н-зустрічі для новачків"}, {"lang": "it", "text": "Incontri Online per Principianti"}]}, {"id": 86, "code": "header.burger.start_path.quick_actions.title", "translations": [{"lang": "ru", "text": "Быстрые действия"}, {"lang": "en", "text": "Quick Actions"}, {"lang": "de", "text": "Schnelle Aktionen"}, {"lang": "ua", "text": "Швидкі дії"}, {"lang": "it", "text": "Azioni Rapide"}]}, {"id": 87, "code": "header.burger.start_path.quick_actions.fill_application", "translations": [{"lang": "ru", "text": "Заполнить заявку"}, {"lang": "en", "text": "Fill Application"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON> ausfüllen"}, {"lang": "ua", "text": "Заповнити заявку"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 88, "code": "header.burger.start_path.quick_actions.beginner_faq", "translations": [{"lang": "ru", "text": "Частые вопросы новичка"}, {"lang": "en", "text": "Beginner FAQ"}, {"lang": "de", "text": "Häufige Fragen für Anfänger"}, {"lang": "ua", "text": "Часті питання новачка"}, {"lang": "it", "text": "FAQ per <PERSON><PERSON>"}]}, {"id": 89, "code": "header.burger.philosophy.title", "translations": [{"lang": "ru", "text": "Философия"}, {"lang": "en", "text": "Philosophy"}, {"lang": "de", "text": "Philosophie"}, {"lang": "ua", "text": "Філософія"}, {"lang": "it", "text": "Filosofia"}]}, {"id": 90, "code": "header.burger.philosophy.schools.title", "translations": [{"lang": "ru", "text": "Школы"}, {"lang": "en", "text": "Schools"}, {"lang": "de", "text": "Schulen"}, {"lang": "ua", "text": "Школи"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 91, "code": "header.burger.philosophy.schools.advaita_vedanta", "translations": [{"lang": "ru", "text": "Адвайта-веданта"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Адвайта-веданта"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 92, "code": "header.burger.philosophy.schools.kashmir_shaivism", "translations": [{"lang": "ru", "text": "Кашмирский шиваизм"}, {"lang": "en", "text": "Kashmir Shaivism"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Кашмірський шиваїзм"}, {"lang": "it", "text": "Shivaismo del Kashmir"}]}, {"id": 93, "code": "header.burger.philosophy.schools.siddha_tradition", "translations": [{"lang": "ru", "text": "Традиция сиддхов"}, {"lang": "en", "text": "Siddha Tradition"}, {"lang": "de", "text": "Siddha-Tradition"}, {"lang": "ua", "text": "Традиція сіддхів"}, {"lang": "it", "text": "Tradizione Siddha"}]}, {"id": 94, "code": "header.burger.philosophy.key_themes.title", "translations": [{"lang": "ru", "text": "Ключевые темы"}, {"lang": "en", "text": "Key Themes"}, {"lang": "de", "text": "Schlüsselthemen"}, {"lang": "ua", "text": "Ключові теми"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 95, "code": "header.burger.philosophy.key_themes.self_nature", "translations": [{"lang": "ru", "text": "Природа Я: сат-чит-ананда"}, {"lang": "en", "text": "Nature of Self: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON> des Selbst: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Природа Я: сат-чіт-ананда"}, {"lang": "it", "text": "Natura del Sé: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 96, "code": "header.burger.philosophy.key_themes.natural_state", "translations": [{"lang": "ru", "text": "Естественное состояние"}, {"lang": "en", "text": "Natural State"}, {"lang": "de", "text": "Natürlicher Zustand"}, {"lang": "ua", "text": "Природний стан"}, {"lang": "it", "text": "Stato Naturale"}]}, {"id": 97, "code": "header.burger.philosophy.key_themes.karma_rebirth", "translations": [{"lang": "ru", "text": "Карма и перерождение"}, {"lang": "en", "text": "Karma and Rebirth"}, {"lang": "de", "text": "Karma und Wiedergeburt"}, {"lang": "ua", "text": "Карма та переродження"}, {"lang": "it", "text": "Karma e Rinascita"}]}, {"id": 98, "code": "header.burger.philosophy.key_themes.basis_path_fruit", "translations": [{"lang": "ru", "text": "Основа, путь, плод"}, {"lang": "en", "text": "Basis, Path, Fruit"}, {"lang": "de", "text": "Grundlage, Weg, Frucht"}, {"lang": "ua", "text": "Основа, шлях, плід"}, {"lang": "it", "text": "Base, Sentiero, Frutto"}]}, {"id": 99, "code": "header.burger.philosophy.key_themes.sahaja_samadhi", "translations": [{"lang": "ru", "text": "Сахаджа-самадхи"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Сахаджа-самадхі"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 100, "code": "header.burger.philosophy.recommended.title", "translations": [{"lang": "ru", "text": "Рекомендуем прочитать"}, {"lang": "en", "text": "Recommended Reading"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Рекомендуємо прочитати"}, {"lang": "it", "text": "Lettura Consigliata"}]}, {"id": 101, "code": "header.burger.philosophy.recommended.master_code", "translations": [{"lang": "ru", "text": "Кодекс Мастера"}, {"lang": "en", "text": "Master's Code"}, {"lang": "de", "text": "Meisterkodex"}, {"lang": "ua", "text": "Кодекс Майстра"}, {"lang": "it", "text": "Codice del Maestro"}]}, {"id": 102, "code": "header.burger.philosophy.recommended.recognition_errors", "translations": [{"lang": "ru", "text": "Ошибки на пути распознавания"}, {"lang": "en", "text": "Errors on the Path of Recognition"}, {"lang": "de", "text": "Fehler auf dem Weg der Erkenntnis"}, {"lang": "ua", "text": "Помилки на шляху розпізнавання"}, {"lang": "it", "text": "Errori sul Sentiero del Riconoscimento"}]}, {"id": 103, "code": "header.burger.philosophy.recommended.yoga_vasishtha", "translations": [{"lang": "ru", "text": "Йога Васиштха"}, {"lang": "en", "text": "Yoga Vasishtha"}, {"lang": "de", "text": "Yoga Vasishtha"}, {"lang": "ua", "text": "Йога Васіштха"}, {"lang": "it", "text": "Yoga Vasishtha"}]}, {"id": 104, "code": "header.burger.philosophy.recommended.tripura_rahasya", "translations": [{"lang": "ru", "text": "Трипура Рахасья"}, {"lang": "en", "text": "<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Тріпура Рахасья"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 105, "code": "header.burger.philosophy.recommended.paraadvaita", "translations": [{"lang": "ru", "text": "Параадвайта"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Параадвайта"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 106, "code": "header.burger.education.programs.title", "translations": [{"lang": "ru", "text": "Программы"}, {"lang": "en", "text": "Programs"}, {"lang": "de", "text": "Programme"}, {"lang": "ua", "text": "Програми"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 107, "code": "header.burger.education.programs.courses", "translations": [{"lang": "ru", "text": "Курсы"}, {"lang": "en", "text": "Courses"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Кур<PERSON>и"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 108, "code": "header.burger.education.programs.vasishtha_institute", "translations": [{"lang": "ru", "text": "Институт Васиштхи"}, {"lang": "en", "text": "Vasishtha Institute"}, {"lang": "de", "text": "Vasishtha-Institut"}, {"lang": "ua", "text": "Інститут Васіштхи"}, {"lang": "it", "text": "Istituto Vasishtha"}]}, {"id": 109, "code": "header.burger.education.programs.full_time_study", "translations": [{"lang": "ru", "text": "Очное обучение"}, {"lang": "en", "text": "Full-time study"}, {"lang": "de", "text": "Vollzeitstudium"}, {"lang": "ua", "text": "Очне навчання"}, {"lang": "it", "text": "Studio a tempo pieno"}]}, {"id": 110, "code": "header.burger.education.programs.part_time_study", "translations": [{"lang": "ru", "text": "Заочное обучение"}, {"lang": "en", "text": "Part-time study"}, {"lang": "de", "text": "Teilzeitstudium"}, {"lang": "ua", "text": "Заочне навчання"}, {"lang": "it", "text": "Studio a tempo parziale"}]}, {"id": 111, "code": "header.burger.education.methodological_systems.title", "translations": [{"lang": "ru", "text": "Методические системы"}, {"lang": "en", "text": "Methodological Systems"}, {"lang": "de", "text": "Methodische Systeme"}, {"lang": "ua", "text": "Методичні системи"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>odolo<PERSON>"}]}, {"id": 112, "code": "header.burger.education.methodological_systems.learning_system", "translations": [{"lang": "ru", "text": "Система обучения"}, {"lang": "en", "text": "Learning System"}, {"lang": "de", "text": "Lernsystem"}, {"lang": "ua", "text": "Система навчання"}, {"lang": "it", "text": "Sistema di Apprendimento"}]}, {"id": 113, "code": "header.burger.education.methodological_systems.methods_tree", "translations": [{"lang": "ru", "text": "Древо методов"}, {"lang": "en", "text": "Tree of Methods"}, {"lang": "de", "text": "Baum der Methoden"}, {"lang": "ua", "text": "Дерево методів"}, {"lang": "it", "text": "Albero dei Metodi"}]}, {"id": 114, "code": "header.burger.education.methodological_systems.seven_lands_wisdom", "translations": [{"lang": "ru", "text": "Семь земель мудрости"}, {"lang": "en", "text": "Seven Lands of Wisdom"}, {"lang": "de", "text": "Sieben Länder der Weisheit"}, {"lang": "ua", "text": "Сім земель мудрості"}, {"lang": "it", "text": "Sette Terre della Saggezza"}]}, {"id": 115, "code": "header.burger.education.methodological_systems.sixteen_kala_stages", "translations": [{"lang": "ru", "text": "16 стадий кала"}, {"lang": "en", "text": "16 Stages of Kala"}, {"lang": "de", "text": "16 <PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "16 стад<PERSON>й кала"}, {"lang": "it", "text": "16 Fasi di Kala"}]}, {"id": 116, "code": "header.burger.education.teaching_yantras.title", "translations": [{"lang": "ru", "text": "Янтры Учения"}, {"lang": "en", "text": "<PERSON><PERSON> of Teaching"}, {"lang": "de", "text": "Yantras der Lehre"}, {"lang": "ua", "text": "Янтри Вчення"}, {"lang": "it", "text": "Yantra dell'Insegnamento"}]}, {"id": 117, "code": "header.burger.education.teaching_yantras.prajna_yantra", "translations": [{"lang": "ru", "text": "Праджня-янтра"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>-ya<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>-ya<PERSON><PERSON>"}, {"lang": "ua", "text": "Праджня-янтра"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>-ya<PERSON><PERSON>"}]}, {"id": 118, "code": "header.burger.education.teaching_yantras.shakti_yantra", "translations": [{"lang": "ru", "text": "Шакти-янтра"}, {"lang": "en", "text": "<PERSON><PERSON>-ya<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON>-ya<PERSON><PERSON>"}, {"lang": "ua", "text": "Шакті-янтра"}, {"lang": "it", "text": "<PERSON><PERSON>-ya<PERSON><PERSON>"}]}, {"id": 119, "code": "header.burger.education.teaching_yantras.nidra_yantra", "translations": [{"lang": "ru", "text": "Нидра-янтра"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>ya<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>ya<PERSON><PERSON>"}, {"lang": "ua", "text": "Нідра-янтра"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>ya<PERSON><PERSON>"}]}, {"id": 120, "code": "header.burger.education.teaching_yantras.nada_yantra", "translations": [{"lang": "ru", "text": "Нада-янтра"}, {"lang": "en", "text": "<PERSON><PERSON>-ya<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON>-ya<PERSON><PERSON>"}, {"lang": "ua", "text": "Нада-янтра"}, {"lang": "it", "text": "<PERSON><PERSON>-ya<PERSON><PERSON>"}]}, {"id": 121, "code": "header.burger.education.teaching_yantras.jyoti_yantra", "translations": [{"lang": "ru", "text": "Джьоти-янтра"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Джйоті-янтра"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 122, "code": "header.burger.education.enrollment_deadlines.title", "translations": [{"lang": "ru", "text": "Набор и дедлайны"}, {"lang": "en", "text": "Enrollment and Deadlines"}, {"lang": "de", "text": "Anmeldung und Fristen"}, {"lang": "ua", "text": "Набір та дедлайни"}, {"lang": "it", "text": "Iscrizioni e Scadenze"}]}, {"id": 123, "code": "header.burger.education.enrollment_deadlines.apply_now", "translations": [{"lang": "ru", "text": "Подать заявку"}, {"lang": "en", "text": "Apply Now"}, {"lang": "de", "text": "Jetzt bewerben"}, {"lang": "ua", "text": "Подати заявку"}, {"lang": "it", "text": "Invia la Domanda"}]}, {"id": 124, "code": "header.burger.community.our_ashrams.title", "translations": [{"lang": "ru", "text": "Наши ашрамы"}, {"lang": "en", "text": "Our Ashrams"}, {"lang": "de", "text": "Unsere Ashrams"}, {"lang": "ua", "text": "Наші ашрами"}, {"lang": "it", "text": "I Nostri <PERSON>"}]}, {"id": 125, "code": "header.burger.community.our_ashrams.shiva_prema_sagar", "translations": [{"lang": "ru", "text": "Шива-Према-Сагар"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"lang": "ua", "text": "Шива-Према-Сагар"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>"}]}, {"id": 126, "code": "header.burger.community.our_ashrams.sadhu_tapovan", "translations": [{"lang": "ru", "text": "Садху-Тапован"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Садху-Тапован"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 127, "code": "header.burger.community.our_ashrams.trimurti_ashram", "translations": [{"lang": "ru", "text": "Тримурти ашрам"}, {"lang": "en", "text": "Trimurti <PERSON>"}, {"lang": "de", "text": "Trimurti <PERSON>"}, {"lang": "ua", "text": "Трімурті ашрам"}, {"lang": "it", "text": "Trimurti <PERSON>"}]}, {"id": 128, "code": "header.burger.community.our_ashrams.shiva_datta_ashram", "translations": [{"lang": "ru", "text": "Шива-Датта ашрам"}, {"lang": "en", "text": "<PERSON>-<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON>-<PERSON><PERSON>"}, {"lang": "ua", "text": "Шива-Датта ашрам"}, {"lang": "it", "text": "<PERSON>-<PERSON><PERSON>"}]}, {"id": 129, "code": "header.burger.community.our_ashrams.avadhuta_siddha_ashram", "translations": [{"lang": "ru", "text": "Авадхута Сиддха ашрам"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Авад<PERSON>ута Сіддха ашрам"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 130, "code": "header.burger.community.our_ashrams.sadhu_loka", "translations": [{"lang": "ru", "text": "Садху Лока"}, {"lang": "en", "text": "<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Садху Лока"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 131, "code": "header.burger.community.our_ashrams.ashram_sukhavati", "translations": [{"lang": "ru", "text": "А<PERSON><PERSON>а<PERSON> Сукхавати"}, {"lang": "en", "text": "<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>хаваті"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 132, "code": "header.burger.community.our_ashrams.chitra_amrita_ashram", "translations": [{"lang": "ru", "text": "Читра Амрита ашрам"}, {"lang": "en", "text": "Chitra Amrita Ashram"}, {"lang": "de", "text": "Chitra Amrita Ashram"}, {"lang": "ua", "text": "Чітра Амріта ашрам"}, {"lang": "it", "text": "Chitra Amrita Ashram"}]}, {"id": 133, "code": "header.burger.community.dharma_centers.title", "translations": [{"lang": "ru", "text": "Дхарма-центры"}, {"lang": "en", "text": "Dharma Centers"}, {"lang": "de", "text": "Dharma<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Дхарма-центри"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON> Dharma"}]}, {"id": 134, "code": "header.burger.community.dharma_centers.kyiv", "translations": [{"lang": "ru", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "en", "text": "Kyiv"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Київ"}, {"lang": "it", "text": "Kiev"}]}, {"id": 135, "code": "header.burger.community.dharma_centers.chisinau", "translations": [{"lang": "ru", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 136, "code": "header.burger.community.dharma_centers.minsk", "translations": [{"lang": "ru", "text": "Минск"}, {"lang": "en", "text": "Minsk"}, {"lang": "de", "text": "Minsk"}, {"lang": "ua", "text": "Мінськ"}, {"lang": "it", "text": "Minsk"}]}, {"id": 137, "code": "header.burger.community.dharma_centers.hamburg", "translations": [{"lang": "ru", "text": "Гамбург"}, {"lang": "en", "text": "Hamburg"}, {"lang": "de", "text": "Hamburg"}, {"lang": "ua", "text": "Гамбург"}, {"lang": "it", "text": "Amburgo"}]}, {"id": 138, "code": "header.burger.community.dharma_centers.almaty", "translations": [{"lang": "ru", "text": "Ал<PERSON>а<PERSON>ы"}, {"lang": "en", "text": "Almaty"}, {"lang": "de", "text": "Almaty"}, {"lang": "ua", "text": "Ал<PERSON><PERSON><PERSON>и"}, {"lang": "it", "text": "Almaty"}]}, {"id": 139, "code": "header.burger.community.dharma_centers.usa", "translations": [{"lang": "ru", "text": "США"}, {"lang": "en", "text": "USA"}, {"lang": "de", "text": "USA"}, {"lang": "ua", "text": "США"}, {"lang": "it", "text": "USA"}]}, {"id": 140, "code": "header.burger.community.people.title", "translations": [{"lang": "ru", "text": "Люди"}, {"lang": "en", "text": "People"}, {"lang": "de", "text": "Menschen"}, {"lang": "ua", "text": "Люди"}, {"lang": "it", "text": "<PERSON>e"}]}, {"id": 141, "code": "header.burger.community.people.instructors", "translations": [{"lang": "ru", "text": "Инструкторы"}, {"lang": "en", "text": "Instructors"}, {"lang": "de", "text": "Instruktoren"}, {"lang": "ua", "text": "Інструктори"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 142, "code": "header.burger.community.people.sannyasins", "translations": [{"lang": "ru", "text": "Санньяси"}, {"lang": "en", "text": "San<PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "San<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Санньясі"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 143, "code": "header.burger.community.people.vedic_family_department", "translations": [{"lang": "ru", "text": "Отдел ведической семьи"}, {"lang": "en", "text": "Vedic Family Department"}, {"lang": "de", "text": "Abteilung für vedische Familie"}, {"lang": "ua", "text": "Відділ ведичної сім'ї"}, {"lang": "it", "text": "Dipartimento della Famiglia Vedica"}]}, {"id": 144, "code": "header.burger.community.people.world_sadhu_assembly", "translations": [{"lang": "ru", "text": "Всемирное собрание садху"}, {"lang": "en", "text": "World Sadhu Assembly"}, {"lang": "de", "text": "Welt-Sadhu-Versammlung"}, {"lang": "ua", "text": "Всесвітнє зібрання садху"}, {"lang": "it", "text": "Assemblea Mondiale dei Sadhu"}]}, {"id": 145, "code": "header.burger.community.create_center.title", "translations": [{"lang": "ru", "text": "Создать центр"}, {"lang": "en", "text": "Create a Center"}, {"lang": "de", "text": "Ein Zentrum gründen"}, {"lang": "ua", "text": "Створити центр"}, {"lang": "it", "text": "Creare un Centro"}]}, {"id": 146, "code": "header.burger.community.create_center.how_to_open", "translations": [{"lang": "ru", "text": "Как открыть дхарма-центр"}, {"lang": "en", "text": "How to Open a Dharma Center"}, {"lang": "de", "text": "Wie man ein <PERSON>-<PERSON><PERSON><PERSON> er<PERSON>"}, {"lang": "ua", "text": "Як відкрити дхарма-центр"}, {"lang": "it", "text": "Come Aprire un Centro Dharma"}]}, {"id": 147, "code": "header.burger.community.create_center.karma_sannyasi_ashram", "translations": [{"lang": "ru", "text": "Ашрам карма-санньяси"}, {"lang": "en", "text": "<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "А<PERSON><PERSON>ам карма-саньясі"}, {"lang": "it", "text": "Ashram dei Karma Sannyasi"}]}, {"id": 148, "code": "header.burger.community.create_center.support_standards", "translations": [{"lang": "ru", "text": "Поддержка и стандарты"}, {"lang": "en", "text": "Support and Standards"}, {"lang": "de", "text": "Unterstützung und Standards"}, {"lang": "ua", "text": "Підтримка та стандарти"}, {"lang": "it", "text": "Supporto e Standard"}]}, {"id": 149, "code": "header.burger.events.calendar.title", "translations": [{"lang": "ru", "text": "Календарь"}, {"lang": "en", "text": "Calendar"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Календар"}, {"lang": "it", "text": "Calendario"}]}, {"id": 150, "code": "header.burger.events.calendar.monthly_schedule", "translations": [{"lang": "ru", "text": "Расписание на месяц"}, {"lang": "en", "text": "Monthly Schedule"}, {"lang": "de", "text": "Monatsplan"}, {"lang": "ua", "text": "Розклад на місяць"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 151, "code": "header.burger.events.calendar.retreats", "translations": [{"lang": "ru", "text": "Ретриты"}, {"lang": "en", "text": "Retreats"}, {"lang": "de", "text": "Retreats"}, {"lang": "ua", "text": "Ретрити"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 152, "code": "header.burger.events.calendar.festivals_homas", "translations": [{"lang": "ru", "text": "Праздники и хомы"}, {"lang": "en", "text": "Festivals and Homas"}, {"lang": "de", "text": "Feste und Homas"}, {"lang": "ua", "text": "Свята та хоми"}, {"lang": "it", "text": "Festival e Homa"}]}, {"id": 153, "code": "header.burger.events.registration.title", "translations": [{"lang": "ru", "text": "Регистрация"}, {"lang": "en", "text": "Registration"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Реєстрація"}, {"lang": "it", "text": "Registrazione"}]}, {"id": 154, "code": "header.burger.events.registration.upcoming_enrollment_events", "translations": [{"lang": "ru", "text": "Ближайшие набираемые события"}, {"lang": "en", "text": "Upcoming Enrollment Events"}, {"lang": "de", "text": "Kommende Anmeldeveranstaltungen"}, {"lang": "ua", "text": "Найближчі події для набору"}, {"lang": "it", "text": "Prossimi Eventi di Iscrizione"}]}, {"id": 155, "code": "header.burger.events.registration.participation_rules", "translations": [{"lang": "ru", "text": "Правила участия"}, {"lang": "en", "text": "Participation Rules"}, {"lang": "de", "text": "Teilnahmeregeln"}, {"lang": "ua", "text": "Правила участі"}, {"lang": "it", "text": "Regole di Partecipazione"}]}, {"id": 156, "code": "header.burger.events.services.title", "translations": [{"lang": "ru", "text": "Услуги"}, {"lang": "en", "text": "Services"}, {"lang": "de", "text": "Dienstleistungen"}, {"lang": "ua", "text": "Послуги"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 157, "code": "header.burger.events.services.order_ritual", "translations": [{"lang": "ru", "text": "Заказать ритуал"}, {"lang": "en", "text": "Order a Ritual"}, {"lang": "de", "text": "Ein Ritual bestellen"}, {"lang": "ua", "text": "Замовити ритуал"}, {"lang": "it", "text": "Ordinare un Rituale"}]}, {"id": 158, "code": "header.burger.events.services.order_homa", "translations": [{"lang": "ru", "text": "Заказать хому"}, {"lang": "en", "text": "Order a Homa"}, {"lang": "de", "text": "<PERSON><PERSON> bestellen"}, {"lang": "ua", "text": "Замовити хому"}, {"lang": "it", "text": "Ordinare una Homa"}]}, {"id": 159, "code": "header.burger.events.services.get_consultation", "translations": [{"lang": "ru", "text": "Получить консультацию"}, {"lang": "en", "text": "Get a Consultation"}, {"lang": "de", "text": "Eine Beratung erhalten"}, {"lang": "ua", "text": "Отримати консультацію"}, {"lang": "it", "text": "Ottenere una Consulenza"}]}, {"id": 160, "code": "header.burger.events.services.invite_monk", "translations": [{"lang": "ru", "text": "Пригласить монаха"}, {"lang": "en", "text": "Invite a <PERSON>"}, {"lang": "de", "text": "Einen Mönch e<PERSON>laden"}, {"lang": "ua", "text": "Запросити монаха"}, {"lang": "it", "text": "Invitare un Monaco"}]}, {"id": 161, "code": "header.burger.events.featured.title", "translations": [{"lang": "ru", "text": "Пригласить монаха"}, {"lang": "en", "text": "Invite a <PERSON>"}, {"lang": "de", "text": "Einen Mönch e<PERSON>laden"}, {"lang": "ua", "text": "Запросити монаха"}, {"lang": "it", "text": "Invitare un Monaco"}]}, {"id": 162, "code": "header.burger.events.featured.seminar_georgia", "translations": [{"lang": "ru", "text": "Семинар в Грузии"}, {"lang": "en", "text": "Seminar in Georgia"}, {"lang": "de", "text": "Seminar in Georgien"}, {"lang": "ua", "text": "Семінар у Грузії"}, {"lang": "it", "text": "Seminario in Georgia"}]}, {"id": 163, "code": "header.burger.events.featured.india_day", "translations": [{"lang": "ru", "text": "День Индии"}, {"lang": "en", "text": "India Day"}, {"lang": "de", "text": "Indien-Tag"}, {"lang": "ua", "text": "День Індії"}, {"lang": "it", "text": "Giornata dell'India"}]}, {"id": 164, "code": "header.burger.media.listen.title", "translations": [{"lang": "ru", "text": "Слушать"}, {"lang": "en", "text": "Listen"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Слухати"}, {"lang": "it", "text": "Ascolta"}]}, {"id": 165, "code": "header.burger.media.listen.lectures", "translations": [{"lang": "ru", "text": "Лекции"}, {"lang": "en", "text": "Lectures"}, {"lang": "de", "text": "Vorträge"}, {"lang": "ua", "text": "Лекції"}, {"lang": "it", "text": "Lezioni"}]}, {"id": 166, "code": "header.burger.media.listen.audiobooks", "translations": [{"lang": "ru", "text": "Аудиокниги"}, {"lang": "en", "text": "Audiobooks"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Аудіокниги"}, {"lang": "it", "text": "Audiolibri"}]}, {"id": 167, "code": "header.burger.media.listen.radio", "translations": [{"lang": "ru", "text": "Радио"}, {"lang": "en", "text": "Radio"}, {"lang": "de", "text": "Radio"}, {"lang": "ua", "text": "Радіо"}, {"lang": "it", "text": "Radio"}]}, {"id": 168, "code": "header.burger.media.listen.bhajans", "translations": [{"lang": "ru", "text": "Бх<PERSON><PERSON><PERSON><PERSON>ны"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Бх<PERSON><PERSON><PERSON><PERSON><PERSON>и"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 169, "code": "header.burger.media.read.title", "translations": [{"lang": "ru", "text": "Читать"}, {"lang": "en", "text": "Read"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Читати"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 170, "code": "header.burger.media.read.books", "translations": [{"lang": "ru", "text": "Книги"}, {"lang": "en", "text": "Books"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Книги"}, {"lang": "it", "text": "Libri"}]}, {"id": 171, "code": "header.burger.media.read.articles", "translations": [{"lang": "ru", "text": "Статьи"}, {"lang": "en", "text": "Articles"}, {"lang": "de", "text": "Artikel"}, {"lang": "ua", "text": "Статті"}, {"lang": "it", "text": "Articoli"}]}, {"id": 172, "code": "header.burger.media.read.sacred_texts", "translations": [{"lang": "ru", "text": "Священные тексты"}, {"lang": "en", "text": "Sacred Texts"}, {"lang": "de", "text": "Heilige Texte"}, {"lang": "ua", "text": "Священні тексти"}, {"lang": "it", "text": "Testi Sacri"}]}, {"id": 173, "code": "header.burger.media.read.upadeshas", "translations": [{"lang": "ru", "text": "Упадеши"}, {"lang": "en", "text": "Upadeshas"}, {"lang": "de", "text": "Upadeshas"}, {"lang": "ua", "text": "Упадеші"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 174, "code": "header.burger.media.read.mythology", "translations": [{"lang": "ru", "text": "Мифология"}, {"lang": "en", "text": "Mythology"}, {"lang": "de", "text": "Mythologie"}, {"lang": "ua", "text": "Міфологія"}, {"lang": "it", "text": "Mitologia"}]}, {"id": 175, "code": "header.burger.media.read.awakened_songs", "translations": [{"lang": "ru", "text": "Песни Пробужденного"}, {"lang": "en", "text": "Songs of the Awakened"}, {"lang": "de", "text": "Lieder des Erwachten"}, {"lang": "ua", "text": "Пісні Пробудженого"}, {"lang": "it", "text": "Canti del Risvegliato"}]}, {"id": 176, "code": "header.burger.media.watch.title", "translations": [{"lang": "ru", "text": "Смотреть"}, {"lang": "en", "text": "Watch"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Дивити<PERSON>я"}, {"lang": "it", "text": "Guarda"}]}, {"id": 177, "code": "header.burger.media.watch.photos", "translations": [{"lang": "ru", "text": "Фото"}, {"lang": "en", "text": "Photos"}, {"lang": "de", "text": "Fotos"}, {"lang": "ua", "text": "Фото"}, {"lang": "it", "text": "Foto"}]}, {"id": 178, "code": "header.burger.media.watch.video_lectures", "translations": [{"lang": "ru", "text": "Видеолекции"}, {"lang": "en", "text": "Video Lectures"}, {"lang": "de", "text": "Videovorträge"}, {"lang": "ua", "text": "Відеолекції"}, {"lang": "it", "text": "Video Lezioni"}]}, {"id": 179, "code": "header.burger.media.collections.title", "translations": [{"lang": "ru", "text": "Подборки"}, {"lang": "en", "text": "Collections"}, {"lang": "de", "text": "Sammlungen"}, {"lang": "ua", "text": "Добірки"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 180, "code": "header.burger.media.collections.for_beginners", "translations": [{"lang": "ru", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "en", "text": "For Beginners"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Новачку"}, {"lang": "it", "text": "<PERSON>"}]}, {"id": 181, "code": "header.burger.media.collections.for_advanced", "translations": [{"lang": "ru", "text": "Продвинутому практикующему"}, {"lang": "en", "text": "For Advanced Practitioners"}, {"lang": "de", "text": "<PERSON><PERSON>r fortgeschrittene Praktizierende"}, {"lang": "ua", "text": "Просунутому практикуючому"}, {"lang": "it", "text": "<PERSON>"}]}, {"id": 182, "code": "header.burger.practice.daily_sadhana.title", "translations": [{"lang": "ru", "text": "Ежедневная садхана"}, {"lang": "en", "text": "Daily Sadhana"}, {"lang": "de", "text": "Tägliches Sadhana"}, {"lang": "ua", "text": "Щоденна садхана"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 183, "code": "header.burger.practice.daily_sadhana.japa_mantras", "translations": [{"lang": "ru", "text": "Джапа и мантры"}, {"lang": "en", "text": "Japa and Mantras"}, {"lang": "de", "text": "Japa und Mantras"}, {"lang": "ua", "text": "Джапа та мантри"}, {"lang": "it", "text": "Japa e Mantra"}]}, {"id": 184, "code": "header.burger.practice.daily_sadhana.upasana", "translations": [{"lang": "ru", "text": "Упасана"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Упасана"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 185, "code": "header.burger.practice.daily_sadhana.concentration", "translations": [{"lang": "ru", "text": "Концентрация"}, {"lang": "en", "text": "Concentration"}, {"lang": "de", "text": "Konzentration"}, {"lang": "ua", "text": "Концентрація"}, {"lang": "it", "text": "Concentrazione"}]}, {"id": 186, "code": "header.burger.practice.daily_sadhana.prostrations", "translations": [{"lang": "ru", "text": "Простирания"}, {"lang": "en", "text": "Prostrations"}, {"lang": "de", "text": "Niederwerfungen"}, {"lang": "ua", "text": "Простирання"}, {"lang": "it", "text": "Prostrazioni"}]}, {"id": 187, "code": "header.burger.practice.daily_sadhana.love_meditation", "translations": [{"lang": "ru", "text": "Медитация любви"}, {"lang": "en", "text": "Love Meditation"}, {"lang": "de", "text": "Liebesmeditation"}, {"lang": "ua", "text": "Медитація любові"}, {"lang": "it", "text": "Meditazione dell'Amore"}]}, {"id": 188, "code": "header.burger.practice.ritual_practice.title", "translations": [{"lang": "ru", "text": "Ритуальная практика"}, {"lang": "en", "text": "Ritual Practice"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Ритуальна практика"}, {"lang": "it", "text": "Pratica Rituale"}]}, {"id": 189, "code": "header.burger.practice.ritual_practice.puja", "translations": [{"lang": "ru", "text": "Пуджа"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Пуджа"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 190, "code": "header.burger.practice.ritual_practice.homa", "translations": [{"lang": "ru", "text": "Хома"}, {"lang": "en", "text": "Ho<PERSON>"}, {"lang": "de", "text": "Ho<PERSON>"}, {"lang": "ua", "text": "Хома"}, {"lang": "it", "text": "Ho<PERSON>"}]}, {"id": 191, "code": "header.burger.practice.ritual_practice.arati", "translations": [{"lang": "ru", "text": "Ара<PERSON>и"}, {"lang": "en", "text": "<PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 192, "code": "header.burger.practice.ritual_practice.abhisheka", "translations": [{"lang": "ru", "text": "Абхишека"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Абхішека"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 193, "code": "header.burger.practice.ritual_practice.festivals_samskaras", "translations": [{"lang": "ru", "text": "Праздники и санскары"}, {"lang": "en", "text": "Festivals and Samskaras"}, {"lang": "de", "text": "Feste und Samskaras"}, {"lang": "ua", "text": "Свята та санскари"}, {"lang": "it", "text": "Festival e Samskara"}]}, {"id": 194, "code": "header.burger.practice.yoga_methods.title", "translations": [{"lang": "ru", "text": "Йога-методы"}, {"lang": "en", "text": "Yoga Methods"}, {"lang": "de", "text": "Yoga-<PERSON><PERSON>"}, {"lang": "ua", "text": "Йога-методи"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 195, "code": "header.burger.practice.yoga_methods.laya_yoga", "translations": [{"lang": "ru", "text": "Лайя-йога"}, {"lang": "en", "text": "<PERSON>a <PERSON>"}, {"lang": "de", "text": "<PERSON>a <PERSON>"}, {"lang": "ua", "text": "Лайя-йога"}, {"lang": "it", "text": "<PERSON>a <PERSON>"}]}, {"id": 196, "code": "header.burger.practice.yoga_methods.karma_yoga", "translations": [{"lang": "ru", "text": "Карма-йога"}, {"lang": "en", "text": "Karma Yoga"}, {"lang": "de", "text": "Karma Yoga"}, {"lang": "ua", "text": "Карма-йога"}, {"lang": "it", "text": "Karma Yoga"}]}, {"id": 197, "code": "header.burger.practice.yoga_methods.bhakti_yoga", "translations": [{"lang": "ru", "text": "Бхакти - йога"}, {"lang": "en", "text": "Bhakti Yoga"}, {"lang": "de", "text": "Bhakti Yoga"}, {"lang": "ua", "text": "Бхакті-йога"}, {"lang": "it", "text": "Bhakti Yoga"}]}, {"id": 198, "code": "header.burger.practice.yoga_methods.raja_yoga", "translations": [{"lang": "ru", "text": "Раджа - йога"}, {"lang": "en", "text": "<PERSON>"}, {"lang": "de", "text": "<PERSON>"}, {"lang": "ua", "text": "Раджа-йога"}, {"lang": "it", "text": "<PERSON>"}]}, {"id": 199, "code": "header.burger.practice.yoga_methods.kundalini_yoga", "translations": [{"lang": "ru", "text": "Кундалини - йога"}, {"lang": "en", "text": "<PERSON><PERSON>lini Yoga"}, {"lang": "de", "text": "<PERSON><PERSON>lini Yoga"}, {"lang": "ua", "text": "Кундал<PERSON>ні-йога"}, {"lang": "it", "text": "<PERSON><PERSON>lini Yoga"}]}, {"id": 200, "code": "header.burger.practice.yoga_methods.hatha_yoga", "translations": [{"lang": "ru", "text": "Хатха - йога"}, {"lang": "en", "text": "Hatha Yoga"}, {"lang": "de", "text": "Hatha Yoga"}, {"lang": "ua", "text": "Хатха-йога"}, {"lang": "it", "text": "Hatha Yoga"}]}, {"id": 201, "code": "header.burger.practice.yoga_methods.shat_chakra_yoga", "translations": [{"lang": "ru", "text": "Шат-чакра-йога"}, {"lang": "en", "text": "S<PERSON>-Chakra-Yoga"}, {"lang": "de", "text": "S<PERSON>-Chakra-Yoga"}, {"lang": "ua", "text": "Шат-чакра-йога"}, {"lang": "it", "text": "S<PERSON>-Chakra-Yoga"}]}, {"id": 202, "code": "header.burger.practice.yoga_methods.mantra_yoga", "translations": [{"lang": "ru", "text": "Мантра - йога"}, {"lang": "en", "text": "Mantra Yoga"}, {"lang": "de", "text": "Mantra Yoga"}, {"lang": "ua", "text": "Мантра-йога"}, {"lang": "it", "text": "Mantra Yoga"}]}, {"id": 203, "code": "header.burger.practice.yoga_methods.everyday_yoga", "translations": [{"lang": "ru", "text": "Йога повседневности"}, {"lang": "en", "text": "Yoga of Daily Life"}, {"lang": "de", "text": "Yoga im täglichen Leben"}, {"lang": "ua", "text": "Йога повсякденності"}, {"lang": "it", "text": "Yoga della Vita Quotidiana"}]}, {"id": 204, "code": "header.burger.practice.meditations.title", "translations": [{"lang": "ru", "text": "Медитации"}, {"lang": "en", "text": "Meditations"}, {"lang": "de", "text": "Meditationen"}, {"lang": "ua", "text": "Медитац<PERSON><PERSON>"}, {"lang": "it", "text": "Meditazioni"}]}, {"id": 205, "code": "header.burger.practice.meditations.atmavichara", "translations": [{"lang": "ru", "text": "Атмавичара"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Атмав<PERSON>чара"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 206, "code": "header.burger.practice.meditations.ma<PERSON>hanti", "translations": [{"lang": "ru", "text": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 207, "code": "header.burger.practice.meditations.niralambha", "translations": [{"lang": "ru", "text": "Нирал<PERSON><PERSON>бха"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Нірала<PERSON>бха"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 208, "code": "header.burger.practice.contemplative_practices.title", "translations": [{"lang": "ru", "text": "Созерцательные практики"}, {"lang": "en", "text": "Contemplative Practices"}, {"lang": "de", "text": "Kontemplative Praktiken"}, {"lang": "ua", "text": "Споглядальні практики"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON> Contemp<PERSON>"}]}, {"id": 209, "code": "header.burger.practice.contemplative_practices.shambhavi_mudra", "translations": [{"lang": "ru", "text": "Шамбхави-мудра"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Шамбхаві-мудра"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 210, "code": "header.burger.practice.contemplative_practices.sankalpas", "translations": [{"lang": "ru", "text": "Санкальпы"}, {"lang": "en", "text": "Sankalpas"}, {"lang": "de", "text": "Sankalpas"}, {"lang": "ua", "text": "Санкальпи"}, {"lang": "it", "text": "Sankalpa"}]}, {"id": 211, "code": "header.burger.practice.contemplative_practices.pure_vision", "translations": [{"lang": "ru", "text": "Чистое видение"}, {"lang": "en", "text": "Pure Vision"}, {"lang": "de", "text": "Reine Vision"}, {"lang": "ua", "text": "Чисте бачення"}, {"lang": "it", "text": "Visione Pura"}]}, {"id": 212, "code": "header.burger.practice.contemplative_practices.one_taste", "translations": [{"lang": "ru", "text": "Единый вкус"}, {"lang": "en", "text": "One Taste"}, {"lang": "de", "text": "Ein Geschmack"}, {"lang": "ua", "text": "Єдиний смак"}, {"lang": "it", "text": "Unico Sapore"}]}, {"id": 213, "code": "header.burger.practice.contemplative_practices.divine_majesty", "translations": [{"lang": "ru", "text": "Божественное величие"}, {"lang": "en", "text": "Divine Majesty"}, {"lang": "de", "text": "Göttliche Majestät"}, {"lang": "ua", "text": "Божественна велич"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 214, "code": "header.burger.practice.contemplative_practices.brahmavichara", "translations": [{"lang": "ru", "text": "Брахмавичара"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Брахмавічара"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 215, "code": "header.burger.practice.energy_work.title", "translations": [{"lang": "ru", "text": "Работа с энергией"}, {"lang": "en", "text": "Energy Work"}, {"lang": "de", "text": "Energiearbeit"}, {"lang": "ua", "text": "Робота з енергією"}, {"lang": "it", "text": "Lavoro Energetico"}]}, {"id": 216, "code": "header.burger.practice.energy_work.chakra_concentration", "translations": [{"lang": "ru", "text": "Концентрация на чакрах"}, {"lang": "en", "text": "Chakra Concentration"}, {"lang": "de", "text": "Chakra-Konzentration"}, {"lang": "ua", "text": "Концентрація на чакрах"}, {"lang": "it", "text": "Concentrazione sui Chakra"}]}, {"id": 217, "code": "header.burger.practice.energy_work.kundalini_rising", "translations": [{"lang": "ru", "text": "Подъем кундалини"}, {"lang": "en", "text": "Kundalini Rising"}, {"lang": "de", "text": "Kundalini-Aufstieg"}, {"lang": "ua", "text": "Під<PERSON>о<PERSON> кундал<PERSON>ні"}, {"lang": "it", "text": "Risveglio della Kundalini"}]}, {"id": 218, "code": "header.burger.practice.energy_work.uniting_elements", "translations": [{"lang": "ru", "text": "Объединение с внешними элементами"}, {"lang": "en", "text": "Uniting with External Elements"}, {"lang": "de", "text": "Vereinigung mit externen Elementen"}, {"lang": "ua", "text": "Об'єднання із зовнішніми елементами"}, {"lang": "it", "text": "Unione con gli Elementi Esterni"}]}, {"id": 219, "code": "header.burger.practice.energy_work.asanas", "translations": [{"lang": "ru", "text": "Асаны"}, {"lang": "en", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Аса<PERSON><PERSON>"}, {"lang": "it", "text": "<PERSON><PERSON>"}]}, {"id": 220, "code": "header.burger.practice.energy_work.subtle_body_structure", "translations": [{"lang": "ru", "text": "Структура тонкого тела"}, {"lang": "en", "text": "Subtle Body Structure"}, {"lang": "de", "text": "Struktur des feinstofflichen Körpers"}, {"lang": "ua", "text": "Структура тонкого тіла"}, {"lang": "it", "text": "Struttura del Corpo Sottile"}]}, {"id": 221, "code": "header.burger.support.forms.title", "translations": [{"lang": "ru", "text": "Формы поддержки"}, {"lang": "en", "text": "Forms of Support"}, {"lang": "de", "text": "Formen der Unterstützung"}, {"lang": "ua", "text": "Форми підтримки"}, {"lang": "it", "text": "Forme di Supporto"}]}, {"id": 222, "code": "header.burger.support.forms.make_donation", "translations": [{"lang": "ru", "text": "Сделать пожертвование"}, {"lang": "en", "text": "Make a Donation"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Зробити пожертву"}, {"lang": "it", "text": "Fai una Donazione"}]}, {"id": 223, "code": "header.burger.support.forms.get_full_access", "translations": [{"lang": "ru", "text": "Получить полный доступ"}, {"lang": "en", "text": "Get Full Access"}, {"lang": "de", "text": "Vollzugriff erhalten"}, {"lang": "ua", "text": "Отримати повний доступ"}, {"lang": "it", "text": "Ottieni l'Accesso Completo"}]}, {"id": 224, "code": "header.burger.support.projects.title", "translations": [{"lang": "ru", "text": "Проекты"}, {"lang": "en", "text": "Projects"}, {"lang": "de", "text": "Projekte"}, {"lang": "ua", "text": "Проекти"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 225, "code": "header.burger.support.projects.land_purchase", "translations": [{"lang": "ru", "text": "Выкуп земли"}, {"lang": "en", "text": "Land Purchase"}, {"lang": "de", "text": "<PERSON><PERSON><PERSON>"}, {"lang": "ua", "text": "Викуп землі"}, {"lang": "it", "text": "Acquisto di Terreni"}]}, {"id": 226, "code": "header.burger.support.projects.temple_construction", "translations": [{"lang": "ru", "text": "Постройка храма"}, {"lang": "en", "text": "Temple Construction"}, {"lang": "de", "text": "Tempelbau"}, {"lang": "ua", "text": "Будівництво храму"}, {"lang": "it", "text": "Costruzione del Tempio"}]}, {"id": 227, "code": "header.burger.support.projects.altar_hall_creation", "translations": [{"lang": "ru", "text": "Создание алтарного зала"}, {"lang": "en", "text": "Creation of an Altar Hall"}, {"lang": "de", "text": "Schaffung einer Altarhalle"}, {"lang": "ua", "text": "Створення вівтарної зали"}, {"lang": "it", "text": "Creazione di una Sala dell'Altare"}]}, {"id": 228, "code": "header.burger.support.participation.title", "translations": [{"lang": "ru", "text": "Участие делом"}, {"lang": "en", "text": "Participation by Dee<PERSON>"}, {"lang": "de", "text": "Teilnahme durch Tat"}, {"lang": "ua", "text": "Участь справою"}, {"lang": "it", "text": "Partecipazione Attiva"}]}, {"id": 229, "code": "header.burger.support.participation.perform_service", "translations": [{"lang": "ru", "text": "Выполнять служение"}, {"lang": "en", "text": "Perform Service"}, {"lang": "de", "text": "<PERSON><PERSON>"}, {"lang": "ua", "text": "Виконувати служіння"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 230, "code": "header.burger.support.participation.partner_projects", "translations": [{"lang": "ru", "text": "Партнерские проекты"}, {"lang": "en", "text": "Partner Projects"}, {"lang": "de", "text": "Partnerprojekte"}, {"lang": "ua", "text": "Партнерські проекти"}, {"lang": "it", "text": "Progetti Partner"}]}, {"id": 231, "code": "header.burger.support.contacts_details.title", "translations": [{"lang": "ru", "text": "Контакты Реквизиты"}, {"lang": "en", "text": "Contacts & Details"}, {"lang": "de", "text": "Kontakte & Details"}, {"lang": "ua", "text": "Контакти та Реквізити"}, {"lang": "it", "text": "Contatti e Dettagli"}]}, {"id": 232, "code": "header.burger.support.contacts_details.accounts_cards", "translations": [{"lang": "ru", "text": "Счета и карты"}, {"lang": "en", "text": "Accounts and Cards"}, {"lang": "de", "text": "Konten und Karten"}, {"lang": "ua", "text": "Рахунки та картки"}, {"lang": "it", "text": "Conti e Carte"}]}, {"id": 233, "code": "header.burger.support.contacts_details.contacts", "translations": [{"lang": "ru", "text": "Контакты"}, {"lang": "en", "text": "Contacts"}, {"lang": "de", "text": "Kontakte"}, {"lang": "ua", "text": "Контакти"}, {"lang": "it", "text": "<PERSON><PERSON><PERSON>"}]}, {"id": 234, "code": "header.burger.forum.category1.title", "translations": [{"lang": "ru", "text": "Категория 1"}, {"lang": "en", "text": "Category 1"}, {"lang": "de", "text": "Kategorie 1"}, {"lang": "ua", "text": "Категорія 1"}, {"lang": "it", "text": "Categoria 1"}]}, {"id": 235, "code": "header.burger.forum.category2.title", "translations": [{"lang": "ru", "text": "Категория 2"}, {"lang": "en", "text": "Category 2"}, {"lang": "de", "text": "Kategorie 2"}, {"lang": "ua", "text": "Категорія 2"}, {"lang": "it", "text": "Categoria 2"}]}, {"id": 236, "code": "header.burger.forum.category3.title", "translations": [{"lang": "ru", "text": "Категория 3"}, {"lang": "en", "text": "Category 3"}, {"lang": "de", "text": "Kategorie 3"}, {"lang": "ua", "text": "Категорія 3"}, {"lang": "it", "text": "Categoria 3"}]}, {"id": 237, "code": "header.burger.forum.category4.title", "translations": [{"lang": "ru", "text": "Категория 4"}, {"lang": "en", "text": "Category 4"}, {"lang": "de", "text": "Kategorie 4"}, {"lang": "ua", "text": "Категорія 4"}, {"lang": "it", "text": "Categoria 4"}]}]