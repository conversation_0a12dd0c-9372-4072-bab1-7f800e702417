[{"sectionNameCode": "header.burger.tradition.title", "activeSection": true, "linkCols": [{"colNameCode": "header.burger.tradition.foundation.title", "activeCol": true, "links": [{"nameCode": "header.burger.tradition.foundation.sanatana_dharma", "url": ""}, {"nameCode": "header.burger.tradition.foundation.creed", "url": ""}, {"nameCode": "header.burger.tradition.foundation.lineage", "url": ""}, {"nameCode": "header.burger.tradition.foundation.paramguru", "url": ""}, {"nameCode": "header.burger.tradition.foundation.juna_akhara", "url": ""}]}, {"colNameCode": "header.burger.tradition.guru_divine.title", "activeCol": false, "links": [{"nameCode": "header.burger.tradition.guru_divine.guru", "url": ""}, {"nameCode": "header.burger.tradition.guru_divine.chosen_deity", "url": ""}, {"nameCode": "header.burger.tradition.guru_divine.deities", "url": ""}, {"nameCode": "header.burger.tradition.guru_divine.siddhi_ethics", "url": ""}]}, {"colNameCode": "header.burger.tradition.vows_path.title", "activeCol": false, "links": [{"nameCode": "header.burger.tradition.vows_path.sannyasa", "url": ""}, {"nameCode": "header.burger.tradition.vows_path.student_code", "url": ""}, {"nameCode": "header.burger.tradition.vows_path.community_history", "url": ""}]}, {"colNameCode": "header.burger.tradition.featured.title", "activeCol": false, "isButtonsCol": true, "links": [{"nameCode": "header.burger.tradition.featured.who_we_are", "url": ""}, {"nameCode": "header.burger.tradition.featured.transmission_structure", "url": ""}, {"nameCode": "header.burger.tradition.featured.ask_question", "url": ""}]}]}, {"sectionNameCode": "header.burger.start_path.title", "activeSection": false, "linkCols": [{"colNameCode": "header.burger.start_path.first_steps.title", "activeCol": false, "links": [{"nameCode": "header.burger.start_path.first_steps.become_student", "url": ""}, {"nameCode": "header.burger.start_path.first_steps.take_diksha", "url": ""}, {"nameCode": "header.burger.start_path.first_steps.monk_consultation", "url": ""}]}, {"colNameCode": "header.burger.start_path.community_life.title", "activeCol": false, "links": [{"nameCode": "header.burger.start_path.community_life.visit_ashram", "url": ""}, {"nameCode": "header.burger.start_path.community_life.perform_service", "url": ""}, {"nameCode": "header.burger.start_path.community_life.become_monk", "url": ""}]}, {"colNameCode": "header.burger.start_path.practical_formats.title", "activeCol": false, "links": [{"nameCode": "header.burger.start_path.practical_formats.retreat", "url": ""}, {"nameCode": "header.burger.start_path.practical_formats.upcoming_events", "url": ""}, {"nameCode": "header.burger.start_path.practical_formats.online_meetings", "url": ""}]}, {"colNameCode": "header.burger.start_path.quick_actions.title", "activeCol": false, "isButtonsCol": true, "links": [{"nameCode": "header.burger.start_path.quick_actions.fill_application", "url": ""}, {"nameCode": "header.burger.start_path.quick_actions.beginner_faq", "url": ""}]}]}, {"sectionNameCode": "header.burger.media.title", "activeSection": false, "linkCols": [{"colNameCode": "header.burger.media.listen.title", "activeCol": false, "links": [{"nameCode": "header.burger.media.listen.lectures", "url": "audiogallery/audiolektsii"}, {"nameCode": "header.burger.media.listen.audiobooks", "url": "library?sortOrder=date&audio=true&paid=false&page=1"}, {"nameCode": "header.burger.media.listen.radio", "url": ""}, {"nameCode": "header.burger.media.listen.bhajans", "url": "audiofiles"}]}, {"colNameCode": "header.burger.media.read.title", "activeCol": false, "links": [{"nameCode": "header.burger.media.read.books", "url": "library"}, {"nameCode": "header.burger.media.read.articles", "url": "categories"}, {"nameCode": "header.burger.media.read.sacred_texts", "url": ""}, {"nameCode": "header.burger.media.read.upadeshas", "url": ""}, {"nameCode": "header.burger.media.read.mythology", "url": ""}, {"nameCode": "header.burger.media.read.awakened_songs", "url": ""}]}, {"colNameCode": "header.burger.media.watch.title", "activeCol": false, "links": [{"nameCode": "header.burger.media.watch.photos", "url": "photo"}, {"nameCode": "header.burger.media.watch.video_lectures", "url": ""}]}, {"colNameCode": "header.burger.media.collections.title", "activeCol": false, "isButtonsCol": true, "links": [{"nameCode": "header.burger.media.collections.for_beginners", "url": ""}, {"nameCode": "header.burger.media.collections.for_advanced", "url": ""}]}]}, {"sectionNameCode": "header.burger.philosophy.title", "activeSection": false, "linkCols": [{"colNameCode": "header.burger.philosophy.schools.title", "activeCol": false, "links": [{"nameCode": "header.burger.philosophy.schools.advaita_vedanta", "url": ""}, {"nameCode": "header.burger.philosophy.schools.kashmir_shaivism", "url": ""}, {"nameCode": "header.burger.philosophy.schools.siddha_tradition", "url": ""}]}, {"colNameCode": "header.burger.philosophy.key_themes.title", "activeCol": false, "links": [{"nameCode": "header.burger.philosophy.key_themes.self_nature", "url": ""}, {"nameCode": "header.burger.philosophy.key_themes.natural_state", "url": ""}, {"nameCode": "header.burger.philosophy.key_themes.karma_rebirth", "url": ""}, {"nameCode": "header.burger.philosophy.key_themes.basis_path_fruit", "url": ""}, {"nameCode": "header.burger.philosophy.key_themes.sahaja_samadhi", "url": ""}]}, {"colNameCode": "header.burger.philosophy.recommended.title", "activeCol": false, "isButtonsCol": true, "links": [{"nameCode": "header.burger.philosophy.recommended.master_code", "url": ""}, {"nameCode": "header.burger.philosophy.recommended.recognition_errors", "url": ""}, {"nameCode": "header.burger.philosophy.recommended.yoga_vasishtha", "url": ""}, {"nameCode": "header.burger.philosophy.recommended.tripura_rahasya", "url": ""}, {"nameCode": "header.burger.philosophy.recommended.paraadvaita", "url": ""}]}]}, {"sectionNameCode": "header.burger.education.title", "activeSection": false, "linkCols": [{"colNameCode": "header.burger.education.programs.title", "activeCol": false, "links": [{"nameCode": "header.burger.education.programs.courses", "url": ""}, {"nameCode": "header.burger.education.programs.vasishtha_institute", "url": ""}, {"nameCode": "header.burger.education.programs.full_time_study", "url": ""}, {"nameCode": "header.burger.education.programs.part_time_study", "url": ""}]}, {"colNameCode": "header.burger.education.methodological_systems.title", "activeCol": false, "links": [{"nameCode": "header.burger.education.methodological_systems.learning_system", "url": ""}, {"nameCode": "header.burger.education.methodological_systems.methods_tree", "url": ""}, {"nameCode": "header.burger.education.methodological_systems.seven_lands_wisdom", "url": ""}, {"nameCode": "header.burger.education.methodological_systems.sixteen_kala_stages", "url": ""}]}, {"colNameCode": "header.burger.education.teaching_yantras.title", "activeCol": false, "links": [{"nameCode": "header.burger.education.teaching_yantras.prajna_yantra", "url": ""}, {"nameCode": "header.burger.education.teaching_yantras.shakti_yantra", "url": ""}, {"nameCode": "header.burger.education.teaching_yantras.nidra_yantra", "url": ""}, {"nameCode": "header.burger.education.teaching_yantras.nada_yantra", "url": ""}, {"nameCode": "header.burger.education.teaching_yantras.jyoti_yantra", "url": ""}]}, {"colNameCode": "header.burger.education.enrollment_deadlines.title", "activeCol": false, "isButtonsCol": true, "links": [{"nameCode": "header.burger.education.enrollment_deadlines.apply_now", "url": ""}]}]}, {"sectionNameCode": "header.burger.community.title", "activeSection": false, "linkCols": [{"colNameCode": "header.burger.community.our_ashrams.title", "activeCol": false, "links": [{"nameCode": "header.burger.community.our_ashrams.shiva_prema_sagar", "url": ""}, {"nameCode": "header.burger.community.our_ashrams.sadhu_tapovan", "url": ""}, {"nameCode": "header.burger.community.our_ashrams.trimurti_ashram", "url": ""}, {"nameCode": "header.burger.community.our_ashrams.shiva_datta_ashram", "url": ""}, {"nameCode": "header.burger.community.our_ashrams.avadhuta_siddha_ashram", "url": ""}, {"nameCode": "header.burger.community.our_ashrams.sadhu_loka", "url": ""}, {"nameCode": "header.burger.community.our_ashrams.ashram_sukhavati", "url": ""}, {"nameCode": "header.burger.community.our_ashrams.chitra_amrita_ashram", "url": ""}]}, {"colNameCode": "header.burger.community.dharma_centers.title", "activeCol": false, "links": [{"nameCode": "header.burger.community.dharma_centers.kyiv", "url": ""}, {"nameCode": "header.burger.community.dharma_centers.chisinau", "url": ""}, {"nameCode": "header.burger.community.dharma_centers.minsk", "url": ""}, {"nameCode": "header.burger.community.dharma_centers.hamburg", "url": ""}, {"nameCode": "header.burger.community.dharma_centers.almaty", "url": ""}, {"nameCode": "header.burger.community.dharma_centers.usa", "url": ""}]}, {"colNameCode": "header.burger.community.people.title", "activeCol": false, "links": [{"nameCode": "header.burger.community.people.instructors", "url": ""}, {"nameCode": "header.burger.community.people.sannyasins", "url": ""}, {"nameCode": "header.burger.community.people.vedic_family_department", "url": ""}, {"nameCode": "header.burger.community.people.world_sadhu_assembly", "url": ""}]}, {"colNameCode": "header.burger.community.create_center.title", "activeCol": false, "isButtonsCol": true, "links": [{"nameCode": "header.burger.community.create_center.how_to_open", "url": ""}, {"nameCode": "header.burger.community.create_center.karma_sannyasi_ashram", "url": ""}, {"nameCode": "header.burger.community.create_center.support_standards", "url": ""}]}]}, {"sectionNameCode": "header.burger.events.title", "activeSection": false, "linkCols": [{"colNameCode": "header.burger.events.calendar.title", "activeCol": false, "links": [{"nameCode": "header.burger.events.calendar.monthly_schedule", "url": ""}, {"nameCode": "header.burger.events.calendar.retreats", "url": ""}, {"nameCode": "header.burger.events.calendar.festivals_homas", "url": ""}]}, {"colNameCode": "header.burger.events.registration.title", "activeCol": false, "links": [{"nameCode": "header.burger.events.registration.upcoming_enrollment_events", "url": ""}, {"nameCode": "header.burger.events.registration.participation_rules", "url": ""}]}, {"colNameCode": "header.burger.events.services.title", "activeCol": false, "links": [{"nameCode": "header.burger.events.services.order_ritual", "url": ""}, {"nameCode": "header.burger.events.services.order_homa", "url": ""}, {"nameCode": "header.burger.events.services.get_consultation", "url": ""}, {"nameCode": "header.burger.events.services.invite_monk", "url": ""}]}, {"colNameCode": "header.burger.events.featured.title", "activeCol": false, "isButtonsCol": true, "links": [{"nameCode": "header.burger.events.featured.seminar_georgia", "url": ""}, {"nameCode": "header.burger.events.featured.india_day", "url": ""}]}]}, {"sectionNameCode": "header.burger.practice.title", "activeSection": false, "linkCols": [{"colNameCode": "header.burger.practice.daily_sadhana.title", "activeCol": false, "links": [{"nameCode": "header.burger.practice.daily_sadhana.japa_mantras", "url": ""}, {"nameCode": "header.burger.practice.daily_sadhana.upasana", "url": ""}, {"nameCode": "header.burger.practice.daily_sadhana.concentration", "url": ""}, {"nameCode": "header.burger.practice.daily_sadhana.prostrations", "url": ""}, {"nameCode": "header.burger.practice.daily_sadhana.love_meditation", "url": ""}]}, {"colNameCode": "header.burger.practice.ritual_practice.title", "activeCol": false, "links": [{"nameCode": "header.burger.practice.ritual_practice.puja", "url": ""}, {"nameCode": "header.burger.practice.ritual_practice.homa", "url": ""}, {"nameCode": "header.burger.practice.ritual_practice.arati", "url": ""}, {"nameCode": "header.burger.practice.ritual_practice.abhisheka", "url": ""}, {"nameCode": "header.burger.practice.ritual_practice.festivals_samskaras", "url": ""}]}, {"colNameCode": "header.burger.practice.yoga_methods.title", "activeCol": false, "links": [{"nameCode": "header.burger.practice.yoga_methods.laya_yoga", "url": ""}, {"nameCode": "header.burger.practice.yoga_methods.karma_yoga", "url": ""}, {"nameCode": "header.burger.practice.yoga_methods.bhakti_yoga", "url": ""}, {"nameCode": "header.burger.practice.yoga_methods.raja_yoga", "url": ""}, {"nameCode": "header.burger.practice.yoga_methods.kundalini_yoga", "url": ""}, {"nameCode": "header.burger.practice.yoga_methods.hatha_yoga", "url": ""}, {"nameCode": "header.burger.practice.yoga_methods.shat_chakra_yoga", "url": ""}, {"nameCode": "header.burger.practice.yoga_methods.mantra_yoga", "url": ""}, {"nameCode": "header.burger.practice.yoga_methods.everyday_yoga", "url": ""}]}, {"colNameCode": "header.burger.practice.meditations.title", "activeCol": false, "links": [{"nameCode": "header.burger.practice.meditations.atmavichara", "url": ""}, {"nameCode": "header.burger.practice.meditations.ma<PERSON>hanti", "url": ""}, {"nameCode": "header.burger.practice.meditations.niralambha", "url": ""}]}, {"colNameCode": "header.burger.practice.contemplative_practices.title", "activeCol": false, "links": [{"nameCode": "header.burger.practice.contemplative_practices.shambhavi_mudra", "url": ""}, {"nameCode": "header.burger.practice.contemplative_practices.sankalpas", "url": ""}, {"nameCode": "header.burger.practice.contemplative_practices.pure_vision", "url": ""}, {"nameCode": "header.burger.practice.contemplative_practices.one_taste", "url": ""}, {"nameCode": "header.burger.practice.contemplative_practices.divine_majesty", "url": ""}, {"nameCode": "header.burger.practice.contemplative_practices.brahmavichara", "url": ""}]}, {"colNameCode": "header.burger.practice.energy_work.title", "activeCol": false, "links": [{"nameCode": "header.burger.practice.energy_work.chakra_concentration", "url": ""}, {"nameCode": "header.burger.practice.energy_work.kundalini_rising", "url": ""}, {"nameCode": "header.burger.practice.energy_work.uniting_elements", "url": ""}, {"nameCode": "header.burger.practice.energy_work.asanas", "url": ""}, {"nameCode": "header.burger.practice.energy_work.subtle_body_structure", "url": ""}]}]}, {"sectionNameCode": "header.burger.support.title", "activeSection": false, "linkCols": [{"colNameCode": "header.burger.support.forms.title", "activeCol": false, "links": [{"nameCode": "header.burger.support.forms.make_donation", "url": ""}, {"nameCode": "header.burger.support.forms.get_full_access", "url": ""}]}, {"colNameCode": "header.burger.support.projects.title", "activeCol": false, "links": [{"nameCode": "header.burger.support.projects.land_purchase", "url": ""}, {"nameCode": "header.burger.support.projects.temple_construction", "url": ""}, {"nameCode": "header.burger.support.projects.altar_hall_creation", "url": ""}]}, {"colNameCode": "header.burger.support.participation.title", "activeCol": false, "links": [{"nameCode": "header.burger.support.participation.perform_service", "url": ""}, {"nameCode": "header.burger.support.participation.partner_projects", "url": ""}]}, {"colNameCode": "header.burger.support.contacts_details.title", "activeCol": false, "isButtonsCol": true, "links": [{"nameCode": "header.burger.support.contacts_details.accounts_cards", "url": ""}, {"nameCode": "header.burger.support.contacts_details.contacts", "url": ""}]}]}, {"sectionNameCode": "header.burger.forum.title", "activeSection": false, "linkCols": [{"colNameCode": "header.burger.forum.category1.title", "activeCol": false, "links": []}, {"colNameCode": "header.burger.forum.category2.title", "activeCol": false, "links": []}, {"colNameCode": "header.burger.forum.category3.title", "activeCol": false, "links": []}, {"colNameCode": "header.burger.forum.category4.title", "activeCol": false, "links": []}]}]