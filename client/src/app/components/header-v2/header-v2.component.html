<div class="header-wrap relative" (mouseleave)="closeLinkMemu()">
    <header 
        [class.scrolled]="isScrolled()"
        [class.link-menu-opened]="activeLinkMenu()"
        [class.burger-menu-opened]="isBurgerMenuOpen()"
        [class.default-bg]="!isMainPage()"
    >
        <div class="left-actions">
            <button class="burger-menu-toggle" (click)="toggleBurgerMenu()" (mouseenter)="this.closeLinkMemu()" [attr.aria-expanded]="isBurgerMenuOpen()"
                [attr.aria-label]="isBurgerMenuOpen() ? ('header.menu.close' | transloco) : ('header.menu.open' | transloco)" type="button">
                <div class="burger-icon" [class.active]="isBurgerMenuOpen()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </button>
            @if(!isBurgerMenuOpen()) {
                <a (click)="navigateToHome()" (mouseenter)="this.closeLinkMemu()">{{'header.nav.main' | transloco}}</a>
                <a [class.active]="activeLinkMenu() === MenuLinkMenuKeys.tradition" (mouseenter)="showLinkMemu(MenuLinkMenuKeys.tradition)">О традиции</a>
                <a [class.active]="activeLinkMenu() === MenuLinkMenuKeys.education" (mouseenter)="showLinkMemu(MenuLinkMenuKeys.education)">Обучение</a>
                <a [class.active]="activeLinkMenu() === MenuLinkMenuKeys.practice" (mouseenter)="showLinkMemu(MenuLinkMenuKeys.practice)">Практика</a>
                <a [class.active]="activeLinkMenu() === MenuLinkMenuKeys.library" (mouseenter)="showLinkMemu(MenuLinkMenuKeys.library)">Библиотека</a>
                <a [class.active]="activeLinkMenu() === MenuLinkMenuKeys.events" (mouseenter)="showLinkMemu(MenuLinkMenuKeys.events)">События</a>
            }
        </div>
        @if(!isBurgerMenuOpen()) {
            <img (click)="navigateToHome()" class="logo" src="../../../assets/images/main-v2/om_big 2.webp" alt="logo">
        } @else {
            <div class="burger-menu-title">
                {{'header.menu.title' | transloco}}
            </div>
        }
        <div class="right-actions">
            @if(!isBurgerMenuOpen() && !isMobileScreen()) {
                <a (click)="navigateToForum()" (mouseenter)="this.closeLinkMemu()">{{'header.nav.forum' | transloco}}</a>
                <a (click)="navigateToDonation()" (mouseenter)="this.closeLinkMemu()">{{'header.nav.support' | transloco}}</a>
            }
            @if(!isMobileScreen() || (isMobileScreen() && isBurgerMenuOpen())) {
                <!-- Language dropdown -->
                <div class="language-menu relative" (click)="toggleLanguageMenu($event)" (mouseenter)="this.closeLinkMemu()" appClickOutside (clickOutside)="closeLanguageMenu()">
                    <a class="cursor-pointer language">Eng</a>
                    @if (isLanguageMenuOpen()) {
                    <div class="dropdown-menu">
                        <div class="dropdown-item" (click)="changeLanguage('ru');$event.stopPropagation();">
                            <span>Русский</span>
                        </div>
                        <div class="dropdown-item" (click)="changeLanguage('en');$event.stopPropagation();">
                            <span>English</span>
                        </div>
                    </div>
                    }
                </div>

                <!-- AI Chat icon -->
                <svg (click)="navigateToAIChat()" class="cursor-pointer" (mouseenter)="this.closeLinkMemu()" width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.0007 1.8875C11.0436 1.6581 11.1653 1.45092 11.3448 1.30182C11.5243 1.15272 11.7504 1.07111 11.9837 1.07111C12.2171 1.07111 12.4431 1.15272 12.6226 1.30182C12.8021 1.45092 12.9239 1.6581 12.9667 1.8875L14.0177 7.4455C14.0924 7.84065 14.2844 8.20412 14.5687 8.48847C14.8531 8.77282 15.2166 8.96486 15.6117 9.0395L21.1697 10.0905C21.3991 10.1333 21.6063 10.2551 21.7554 10.4346C21.9045 10.6141 21.9861 10.8401 21.9861 11.0735C21.9861 11.3069 21.9045 11.5329 21.7554 11.7124C21.6063 11.8919 21.3991 12.0137 21.1697 12.0565L15.6117 13.1075C15.2166 13.1821 14.8531 13.3742 14.5687 13.6585C14.2844 13.9429 14.0924 14.3064 14.0177 14.7015L12.9667 20.2595C12.9239 20.4889 12.8021 20.6961 12.6226 20.8452C12.4431 20.9943 12.2171 21.0759 11.9837 21.0759C11.7504 21.0759 11.5243 20.9943 11.3448 20.8452C11.1653 20.6961 11.0436 20.4889 11.0007 20.2595L9.94972 14.7015C9.87507 14.3064 9.68304 13.9429 9.39869 13.6585C9.11433 13.3742 8.75087 13.1821 8.35572 13.1075L2.79772 12.0565C2.56832 12.0137 2.36113 11.8919 2.21204 11.7124C2.06294 11.5329 1.98132 11.3069 1.98132 11.0735C1.98132 10.8401 2.06294 10.6141 2.21204 10.4346C2.36113 10.2551 2.56832 10.1333 2.79772 10.0905L8.35572 9.0395C8.75087 8.96486 9.11433 8.77282 9.39869 8.48847C9.68304 8.20412 9.87507 7.84065 9.94972 7.4455L11.0007 1.8875Z" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>

                <!-- Theme dropdown -->
                <div class="theme-menu relative" (click)="toggleThemeMenu($event)" (mouseenter)="this.closeLinkMemu()" appClickOutside (clickOutside)="closeThemeMenu()">
                    <svg class="cursor-pointer theme" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z"
                            stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M12 2L12 4" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M12 20L12 22" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M4.92969 4.92969L6.33969 6.33969" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path d="M17.6562 17.6602L19.0662 19.0702" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path d="M2 12L4 12" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M20 12L22 12" stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M6.33969 17.6602L4.92969 19.0702" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path d="M19.0662 4.92969L17.6562 6.33969" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                    @if (isThemeMenuOpen()) {
                    <div class="dropdown-menu">
                        <div class="dropdown-item" (click)="changeTheme('light');$event.stopPropagation();">
                            <span>Светлая</span>
                        </div>
                        <div class="dropdown-item" (click)="changeTheme('dark');$event.stopPropagation();">
                            <span>Темная</span>
                        </div>
                        <!-- <div class="dropdown-item" (click)="changeTheme('auto');$event.stopPropagation();">
                            <span>Авто</span>
                        </div> -->
                    </div>
                    }
                </div>

                <!-- Search icon -->
                <svg class="cursor-pointer" [routerLink]="['/', currentLanguage(), 'search']" (click)="this.closeBurgerMenu()" (mouseenter)="this.closeLinkMemu()" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20.9963 21.0002L16.6562 16.6602" stroke="#351F04" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                    <path
                        d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
                        stroke="#351F04" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>

                @if (profileService.isAuth) {
                    <!-- Авторизованный пользователь -->
                    <div class="user-menu auth_ relative" (click)="toggleUserMenu($event)" (mouseenter)="this.closeLinkMemu()" appClickOutside (clickOutside)="closeUserMenu()">
                        <div class="relative cursor-pointer">
                            <div class="notifications">
                                <div class="user_logo_ authorized_">
                                    @if (profileService.profile && profileService.profile.avatar && profileService.profile.avatar.name) {
                                    <img ngSrc="{{environment.serverUrl + '/upload/' + profileService.avatar()}}" width="32" height="32" loading="lazy" alt="logo">
                                    } @else {
                                    <img ngSrc="assets/images/user_logo.webp" width="32" height="32" loading="lazy" alt="logo">
                                    }
                                </div>
                                <span *ngIf="profileService.profile?.unreadNotificationsCount" class="notify-counter notify-absolute">
                                    {{profileService.profile!.unreadNotificationsCount > 100 ? '99+' : profileService.profile!.unreadNotificationsCount}}
                                </span>
                            </div>
                        </div>
                        <!-- <div class="user-dropdown has-arrow">{{ profileService.name() === "null" ? '' : profileService.name() }}</div> -->

                        <!-- Выпадающее меню -->
                        @if (isUserMenuOpen()) {
                        <div class="dropdown-menu">
                            <div class="dropdown-item" (click)="navigateToProfile();$event.stopPropagation();">
                                <span>Профиль</span>
                            </div>
                            <div class="dropdown-item" (click)="navigateToFavorites();$event.stopPropagation();">
                                <span>Избранное</span>
                            </div>
                            <div class="dropdown-item" (click)="navigateToPlaylists();$event.stopPropagation();">
                                <span>Плейлисты</span>
                            </div>
                            <div class="dropdown-item" (click)="navigateToMyData();$event.stopPropagation();">
                                <span>Мои данные</span>
                            </div>
                            <div class="dropdown-item" (click)="navigateToAnketa();$event.stopPropagation();">
                                <span>Анкета</span>
                            </div>
                            <div class="dropdown-item" (click)="navigateToSubscriptions();$event.stopPropagation();">
                                <span>Подписки</span>
                            </div>
                            <div class="dropdown-item" (click)="$event.stopPropagation(); router.navigate(['/ru/notifications'])">
                                <span>Уведомления</span>
                                <span *ngIf='profileService.profile && profileService.profile!.unreadNotificationsCount > 0' class='notify-counter'>
                                    {{profileService.profile!.unreadNotificationsCount > 100 ? '99+' : profileService.profile!.unreadNotificationsCount}}
                                </span>
                            </div>
                            <div class="dropdown-item" (click)="logout();$event.stopPropagation();">
                                <span>Выйти</span>
                            </div>
                        </div>
                        }
                    </div>
                    } @else {
                    <!-- Неавторизованный пользователь -->
                    <a [routerLink]="['/ru/signin']" (click)="this.closeBurgerMenu()" (mouseenter)="this.closeLinkMemu()">
                        Войти
                    </a>
                    }
            } 

            @if(isMobileScreen() && !isBurgerMenuOpen()) {
                <div class="today-btn" (click)="showSidebar(); this.closeLinkMemu()" (mouseenter)="this.closeLinkMemu()">
                     <img src="../../../assets/images/main-v2/wheel-menu.webp" alt="wheel">
                        Сегодня
                </div>
            }
    
        </div>
    </header>
    @if(selectedLinkMenu().length) {
        <div class="link-menu" [class.opened]="activeLinkMenu()">
            <div class="flex flex-wrap gap-[30px] w-full">
                @for (linkMenuItem of selectedLinkMenu(); track $index) {
                    <div class="link-menu-item" (click)="navigateToDropdownMenuLink(linkMenuItem.link)">
                        <div class="item-img" [style.background-image]="'url(' + linkMenuItem.imgUrl + ')'">
                            <div class="img-mask"></div>
                        </div>
                        <div class="link-menu-item-text-content">
                            <div class="title">{{linkMenuItem.nameCode ? (linkMenuItem.nameCode | transloco) : linkMenuItem.name}}</div>
                            @if(linkMenuItem.description) {
                                <div class="description">
                                    {{linkMenuItem.description}}
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
            <div class="show-more">{{'header.menu.show_more' | transloco}}</div>
        </div>
    }
</div>

@if(isBurgerMenuOpen()) {
    <div class="desktop-burger-menu">
        <div class="left-link-bar">
            @for (section of burgrerMenu(); track $index) {
                <div 
                    class="link-bar-item" 
                    [class.active]="section.activeSection"
                    (click)="selectBurgerMenuCol(section)"
                >
                    @if(section.activeSection) {
                        <img src="../../../assets/images/main-v2/wheel-menu.webp" alt="wheel">
                    }
                    <span>
                        {{section.sectionNameCode | transloco}}
                    </span>
                </div>
            }
        </div>
        <div class="burger-menu-main-content" #burgerMenuContent>
            @for (section of burgrerMenu(); track $index) {
                <section [class.active]="section.activeSection">
                    <div class="section-title" (click)="toggleBurgerMenuSection(section)">
                        @if(section.activeSection) {
                            <img src="../../../assets/images/main-v2/wheel-menu.webp" alt="wheel">
                        }
                        {{section.sectionNameCode | transloco}}
                    </div>
                    <div class="links-cols">
                        @for (col of section.linkCols; track $index) {
                            <div 
                                class="links-col" 
                                [class.active-col]="col.activeCol"
                                [class.buttons-col]="col.isButtonsCol"
                                (click)="selectBurgerMenuCol(col)"
                            >
                                <div class="col-name" (click)="toggleBurgerMenuSectionCol(section, col); $event.stopPropagation()">
                                    <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M1 1.5L6 6.5L11 1.5" stroke="#532E00" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>

                                    {{getColName(col)}}
                                </div>
                                <div class="links-wrapper">
                                    @for (link of col.links; track $index) {
                                        @if (col.isButtonsCol && !isMobileScreen()) {
                                            <button (click)="navigateToBurgerMenuLink(link.url)">
                                                <span>
                                                    {{getLinkName(link)}}
                                                </span>
                                            </button>
                                        } @else {
                                            <a (click)="navigateToBurgerMenuLink(link.url)">{{getLinkName(link)}}</a>
                                        }
                                    }
                                </div>
                            </div>
                        }
                    </div> 
                </section>
            }
            @if (isMobileScreen()) {
                <div class="menu-mobile-footer">
                    <svg (click)="navToSocialMedia()" width="22" height="19" viewBox="0 0 22 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 7.38517C0.161541 6.74676 0.613856 6.44352 1.22771 6.24668C7.5332 4.20378 13.8333 2.14492 20.1388 0.0913801C21.0327 -0.201223 21.8457 0.229701 21.9857 1.08623C22.0181 1.28839 21.9911 1.51183 21.9481 1.71399C20.6826 7.05532 19.4119 12.3913 18.1411 17.7327C17.8772 18.8286 16.7787 19.2063 15.8903 18.4934C14.5872 17.4507 13.2948 16.4026 12.0025 15.344C11.8625 15.2322 11.7817 15.2322 11.6417 15.3386C10.6186 16.1047 9.59016 16.8602 8.56706 17.6209C8.50245 17.6688 8.44322 17.7114 8.34629 17.7752C8.35706 17.7007 8.35706 17.6529 8.36783 17.6156C8.82014 15.9877 9.27784 14.3597 9.73554 12.7318C9.76785 12.6254 9.84324 12.519 9.92401 12.4339C12.2071 10.0665 14.4956 7.70437 16.7841 5.34227C16.8541 5.27311 16.9187 5.19331 16.9726 5.11351C17.1234 4.89006 17.1126 4.6081 16.9564 4.3953C16.7949 4.1825 16.5095 4.08674 16.2457 4.17186C16.1433 4.20378 16.0464 4.25698 15.9549 4.30486C12.261 6.32648 8.57245 8.35341 4.88393 10.3804C4.73854 10.4602 4.62008 10.4602 4.4693 10.3963C3.33852 9.90155 2.20773 9.39614 1.06617 8.92798C0.527701 8.70454 0.134618 8.38533 0 7.81077C0 7.66713 0 7.52349 0 7.38517Z" fill="#99601A"/>
                        <path d="M12.8965 7.50229C12.4872 7.9279 12.078 8.3535 11.6634 8.77378C10.7318 9.73671 9.80565 10.6996 8.86871 11.6573C8.69102 11.8435 8.57255 12.0456 8.50255 12.2903C8.04485 13.9449 7.58177 15.5994 7.11868 17.2539C7.10253 17.3178 7.0756 17.3816 7.03253 17.5093C6.90329 16.9135 6.78483 16.3815 6.66637 15.8494C6.35405 14.4237 6.04713 13.0032 5.72943 11.5828C5.69712 11.4338 5.72943 11.3593 5.86943 11.2848C8.14716 10.04 10.4249 8.78975 12.7026 7.54485C12.7565 7.51293 12.8103 7.49165 12.8695 7.46505C12.8803 7.47569 12.8911 7.49165 12.8965 7.50229Z" fill="#99601A"/>
                    </svg>
                    <svg (click)="navToSocialMedia()" width="27" height="18" viewBox="0 0 27 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M10.8457 0.0118861C13.3324 -0.0126515 15.8192 -0.00216883 18.3008 0.0714564C19.8347 0.115635 21.3738 0.238341 22.8975 0.444503C24.8288 0.709601 26.0312 1.93673 26.3828 3.79216C26.6172 5.03888 26.6942 6.30025 26.7197 7.56169C26.7605 9.45152 26.76 11.3418 26.5205 13.2267C26.4441 13.8449 26.3527 14.4827 26.1338 15.0617C25.5732 16.5489 24.4161 17.3788 22.8008 17.6047C20.7522 17.8943 18.6877 17.9629 16.6289 17.9875C14.1778 18.0169 11.7214 18.0025 9.27539 17.9338C7.70592 17.8896 6.13134 17.7669 4.57715 17.5558C2.65089 17.3006 1.40206 16.0587 1.10645 14.2033C0.953586 13.207 0.897792 12.1958 0.795898 11.1896C0.78061 11.0473 0.765288 10.8994 0.75 10.757V7.28728C0.780574 6.81608 0.795516 6.34439 0.851562 5.8781C0.953466 5.05358 1.01974 4.22403 1.20312 3.41423C1.57003 1.76986 2.94121 0.620545 4.67383 0.389816C6.72244 0.114932 8.7869 0.0315207 10.8457 0.0118861ZM11.1719 12.9465C13.5413 11.6262 15.8851 10.3205 18.2646 8.99528C15.88 7.6652 13.5413 6.36438 11.1719 5.04411V12.9465Z" fill="#99601A"/>
                    </svg>
    
                    <svg (click)="navToSocialMedia()" width="28" height="15" viewBox="0 0 28 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0.5 1.09932C0.654173 0.853128 0.888515 0.73904 1.17836 0.721026C1.3202 0.715022 1.46204 0.703013 1.59771 0.697008C2.73859 0.691003 3.88563 0.684999 5.02651 0.678994C5.61236 0.67299 6.00088 0.931188 6.25989 1.45959C6.94442 2.84666 7.64744 4.22172 8.37514 5.58476C8.67731 6.1552 9.06583 6.6776 9.59618 7.08592C9.71952 7.18199 9.8552 7.26606 10.0032 7.3201C10.33 7.44619 10.5767 7.33811 10.7432 7.04389C10.9961 6.60555 11.0146 6.11317 11.0762 5.6268C11.2119 4.50393 11.1934 3.39308 10.9591 2.28223C10.8172 1.61571 10.404 1.26144 9.74419 1.12934C9.40501 1.06329 9.38651 0.979225 9.62085 0.727031C9.89836 0.432805 10.2375 0.240657 10.6446 0.180611C12.1678 -0.0415596 13.691 -0.0715826 15.2142 0.162597C15.9666 0.276685 16.2503 0.528879 16.3181 1.39354C16.3674 2.05405 16.3119 2.72657 16.3058 3.39308C16.2934 4.28176 16.2749 5.17045 16.2811 6.06513C16.2811 6.32333 16.3428 6.59354 16.4353 6.83973C16.6388 7.36213 17.052 7.44019 17.4837 7.08592C18.1928 6.50947 18.6677 5.7589 19.1549 5.02033C19.8949 3.89146 20.4993 2.69654 21.0111 1.45959C21.2825 0.799086 21.4058 0.727031 22.1273 0.721026C23.5457 0.709017 24.9579 0.684999 26.3763 0.678994C26.5922 0.678994 26.8203 0.703013 27.0239 0.763059C27.4247 0.877146 27.5542 1.09932 27.4802 1.50163C27.3692 2.1201 27.0732 2.67253 26.7155 3.17691C25.7966 4.4619 24.8408 5.71687 23.9034 6.98984C23.6937 7.27206 23.4779 7.55428 23.299 7.86051C22.9907 8.37691 23.04 8.83926 23.4656 9.26559C24.0822 9.88407 24.7298 10.4785 25.3588 11.091C26.0433 11.7575 26.697 12.454 27.1904 13.2707C27.289 13.4388 27.3754 13.6189 27.4309 13.8051C27.622 14.4716 27.289 14.8259 26.6292 14.8919C26.0372 14.952 25.4328 14.94 24.8408 14.952C24.0699 14.97 23.2929 15 22.522 15C21.8622 15 21.2701 14.7658 20.7829 14.3575C20.2464 13.9072 19.7777 13.3848 19.2782 12.8924C18.9144 12.5321 18.5567 12.1658 18.1867 11.8176C18.088 11.7215 17.9647 11.6494 17.8413 11.5894C17.5145 11.4273 17.274 11.4633 17.0026 11.7035C16.5216 12.1238 16.4106 12.6942 16.3366 13.2767C16.3058 13.5289 16.3058 13.7871 16.2934 14.0453C16.2564 14.5677 16.0036 14.8679 15.4732 14.9099C13.1545 15.0961 10.9529 14.8259 8.96099 13.4808C7.63511 12.5861 6.56207 11.4933 5.6062 10.2503C3.54645 7.61432 1.97389 4.70809 0.604837 1.68777C0.567836 1.60971 0.530835 1.53765 0.5 1.45959C0.5 1.3395 0.5 1.21941 0.5 1.09932Z" fill="#99601A"/>
                    </svg>
    
                    <svg (click)="navToSocialMedia()" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.4941 0C11.995 0 12.4958 0 12.9967 0C13.2847 0.0375489 13.5664 0.0625815 13.8544 0.106389C16.4275 0.469361 18.6876 1.51447 20.5408 3.34811C23.6648 6.43338 24.8418 10.1757 23.9716 14.4688C23.2266 18.1361 21.0854 20.8396 17.7986 22.6295C17.0974 23.0112 16.3586 23.2991 15.5886 23.5119C15.5886 20.5518 15.5886 17.6104 15.5886 14.6441C16.4526 14.6441 17.2915 14.6441 18.1492 14.6441C18.2807 13.6428 18.4059 12.6602 18.5374 11.6402C17.5294 11.6402 16.559 11.6402 15.5886 11.6402C15.5886 10.8391 15.5573 10.0694 15.6011 9.29961C15.6324 8.73012 15.9454 8.41721 16.5152 8.33585C16.9471 8.27327 17.3854 8.29205 17.8236 8.27953C18.0866 8.27327 18.3495 8.27953 18.6125 8.27953C18.6125 7.3721 18.6125 6.4897 18.6125 5.58227C18.049 5.55098 17.4981 5.49465 16.9471 5.50091C16.3712 5.50717 15.7827 5.49465 15.2255 5.61982C13.604 5.97027 12.5772 7.23442 12.4896 8.93038C12.452 9.73768 12.4708 10.5512 12.4583 11.3648C12.4583 11.4587 12.4583 11.5463 12.4583 11.6652C11.588 11.6652 10.7491 11.6652 9.90392 11.6652C9.90392 12.679 9.90392 13.6615 9.90392 14.6754C10.7616 14.6754 11.6006 14.6754 12.4395 14.6754C12.4395 17.7982 12.4395 20.8897 12.4395 23.9875C12.4019 23.9937 12.3769 24 12.3518 24C12.2892 24 12.2266 24 12.164 24C10.4298 23.9812 8.76448 23.6433 7.20558 22.8861C3.49928 21.0649 1.23292 18.1299 0.437819 14.0808C0.35017 13.6365 0.312606 13.1859 0.25 12.7416C0.25 12.2409 0.25 11.7403 0.25 11.2396C0.268782 11.0832 0.287564 10.933 0.306346 10.7765C0.588075 8.18566 1.58352 5.90143 3.33024 3.96767C5.18339 1.92751 7.47479 0.650847 10.2044 0.181486C10.6302 0.106389 11.0684 0.0625815 11.4941 0Z" fill="#99601A"/>
                    </svg>
    
                    <svg (click)="navToSocialMedia()" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22.0034 10.9995C22.0034 13.0171 22.0125 15.0393 21.9988 17.0569C21.9851 19.4661 20.4005 21.4108 18.0601 21.9027C17.7277 21.971 17.3771 21.9984 17.0355 21.9984C13.0149 22.0029 8.98962 22.0075 4.96894 21.9984C2.54651 21.9938 0.588532 20.4043 0.0967606 18.0588C0.028459 17.7355 0.00113836 17.3939 0.00113836 17.0614C0.00113836 13.0262 -0.00341508 8.9865 0.0056918 4.94676C0.0102452 2.54205 1.6085 0.58822 3.94442 0.100901C4.27682 0.0325852 4.62743 0.00525895 4.96894 0.00525895C8.98962 0.000704565 13.0149 -0.00384982 17.0355 0.00525895C19.458 0.00981333 21.416 1.59474 21.9032 3.9448C21.9806 4.30915 21.9988 4.69627 21.9988 5.06973C22.0079 7.04633 22.0034 9.02293 22.0034 10.9995ZM18.1284 11.0041C18.1284 7.07821 14.9546 3.89015 11.025 3.87648C7.08173 3.87193 3.88522 7.05089 3.87611 10.9904C3.87156 14.9254 7.05897 18.1226 10.9931 18.1271C14.9319 18.1317 18.1238 14.9436 18.1284 11.0041ZM19.7858 3.66243C19.7858 2.82442 19.1165 2.15493 18.2832 2.15493C17.4499 2.15493 16.7806 2.82442 16.776 3.66243C16.776 4.49132 17.4545 5.16993 18.2832 5.16993C19.1165 5.16993 19.7858 4.49588 19.7858 3.66243Z" fill="#99601A"/>
                        <path d="M15.0368 10.9942C15.0413 13.2258 13.2518 15.0294 11.0207 15.0385C8.78037 15.0476 6.97266 13.244 6.97266 11.0033C6.97266 8.77165 8.76671 6.97266 11.0024 6.97266C13.2336 6.96811 15.0322 8.76254 15.0368 10.9942Z" fill="#99601A"/>
                    </svg>
    
                </div>
            }
        </div>
    </div>
}