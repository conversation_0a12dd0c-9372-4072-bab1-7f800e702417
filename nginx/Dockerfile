FROM nginx:alpine

RUN rm /etc/nginx/conf.d/default.conf

COPY docker.conf /etc/nginx/conf.d/default.conf

RUN mkdir -p /var/log/nginx

RUN mkdir -p /app/client/public \
    /app/client/dist/client/browser/media \
    /app/client/dist/browser/media \
    /app/server/upload \
    /app/admin/dist/admin/browser \
    /app/.well-known

RUN chown -R nginx:nginx /app /var/log/nginx

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
