import { BadRequestException } from '@nestjs/common'

export class UrlValidator {
  private static readonly ALLOWED_PROTOCOLS = ['http:', 'https:'];
  private static readonly BLOCKED_DOMAINS = [
    'localhost',
    '127.0.0.1',
    '0.0.0.0',
    '::1'
  ];

  static validateReturnUrl(url: string, allowLocalhost: boolean = false): string {
    if (!url) {
      throw new BadRequestException('Return URL не может быть пустым');
    }

    let parsedUrl: URL;
    try {
      parsedUrl = new URL(url);
    } catch (error) {
      throw new BadRequestException('Невалидный return URL');
    }

    if (!this.ALLOWED_PROTOCOLS.includes(parsedUrl.protocol)) {
      throw new BadRequestException('Return URL должен использовать HTTP или HTTPS протокол');
    }

    if (!allowLocalhost && this.isBlockedDomain(parsedUrl.hostname)) {
      throw new BadRequestException('Return URL не может указывать на локальные адреса');
    }

    if (url.length > 2048) {
      throw new BadRequestException('Return URL слишком длинный');
    }

    return url;
  }

  private static isBlockedDomain(hostname: string): boolean {
    return this.BLOCKED_DOMAINS.some(blocked => 
      hostname === blocked || hostname.endsWith('.' + blocked)
    );
  }

  static createFallbackUrl(baseUrl: string, path: string = '/ru/donation?success=1'): string {
    try {
      const url = new URL(path, baseUrl);
      return url.toString();
    } catch (error) {
      return `${baseUrl}${path}`;
    }
  }
}
