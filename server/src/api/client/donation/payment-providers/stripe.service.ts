import { UrlValidator } from '@/common/utils/url-validator.util'
import { Injectable, InternalServerErrorException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import Stripe from 'stripe'

@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(private readonly configService: ConfigService) {
    this.stripe = new Stripe(this.configService.get<string>('STRIPE_SECRET_KEY'));
  }

  async createSubscriptionCheckout(priceIdOrLineItems: string | any[], customerEmail: string | null, description: string, metadata = {}, returnUrl?: string) {
    try {
      const isDevelopment = process.env.BUILD_ENV === 'local';
      let successUrl = this.configService.get<string>('DONATION_SUCCESS_URL');

      if (returnUrl) {
        try {
          successUrl = UrlValidator.validateReturnUrl(returnUrl, isDevelopment);
        } catch (error) {
        }
      }

      const lineItems = typeof priceIdOrLineItems === 'string'
        ? [{ price: priceIdOrLineItems, quantity: 1 }]
        : priceIdOrLineItems;

      const session = await this.stripe.checkout.sessions.create({
        mode: 'subscription',
        payment_method_types: ['card', 'paypal'],
        line_items: lineItems,
        success_url: successUrl,
        customer_email: customerEmail || undefined,
        metadata,
        subscription_data: {
          metadata,
          description
        },
      });

      return { paymentUrl: session.url };
    } catch (error) {
      throw new InternalServerErrorException('Ошибка при создании подписки через Stripe');
    }
  }

  async createPayment(amount: number, currency: string, description: string, metadata: any = {}, returnUrl?: string) {
    try {
      const isDevelopment = process.env.BUILD_ENV === 'local';
      let successUrl = this.configService.get<string>('DONATION_SUCCESS_URL');

      if (returnUrl) {
        try {
          successUrl = UrlValidator.validateReturnUrl(returnUrl, isDevelopment);
        } catch (error) {
        }
      }

      if(metadata?.autoRenew) {
        const session = await this.stripe.checkout.sessions.create({
          mode: 'subscription',
          payment_method_types: ['card', 'paypal'],
          line_items: [
            {
              quantity: 1,
              price_data: {
                currency,
                unit_amount: amount,
                product_data: {
                  name: description,
                  description,
                },
                recurring: {
                  interval: 'month',
                  interval_count: 1,
                },
              },
            },
          ],
          success_url: successUrl,
          subscription_data: { description, metadata },
        });

        return { paymentUrl: session.url };
      } else {
        const session = await this.stripe.checkout.sessions.create({
          payment_method_types: ['card', 'paypal'],
          line_items: [
            {
              price_data: {
                currency: currency.toLowerCase(),
                product_data: {
                  name: description,
                  description,
                },
                unit_amount: Math.round(amount * 100),
              },
              quantity: 1,
            },
          ],
          mode: 'payment',
          success_url: successUrl,
          metadata,
          payment_intent_data: {
            description,
            metadata
          }
        });

        return { paymentUrl: session.url };
      }
    } catch (error) {
      throw new InternalServerErrorException('Ошибка при создании платежа через Stripe');
    }
  }

  async cancelAutoRenew(subscriptionId: string) {
    return await this.stripe.subscriptions.update(subscriptionId, { cancel_at_period_end: true });
  }

  async getPaymentIntent(paymentIntentId: string) {
    try {
      return await this.stripe.paymentIntents.retrieve(paymentIntentId, {
        expand: ['payment_method']
      });
    } catch (error) {
      return null;
    }
  }

  async getPaymentMethod(paymentMethodId: string) {
    try {
      return await this.stripe.paymentMethods.retrieve(paymentMethodId);
    } catch (error) {
      return null;
    }
  }
}