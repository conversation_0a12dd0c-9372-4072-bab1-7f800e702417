import { IsBoolean, IsEnum, IsN<PERSON>ber, IsOptional, IsString, IsUrl } from 'class-validator';

export enum PaymentProviderType {
  YOOKASSA = 'yookassa',
  STRIPE = 'stripe',
}

export class CreatePaymentDto {
  @IsEnum(PaymentProviderType)
  type: PaymentProviderType;

  @IsNumber()
  sum: number;

  @IsBoolean()
  @IsOptional()
  autoRenew?: boolean;

  @IsString()
  @IsOptional()
  comment?: string;

  @IsUrl()
  @IsOptional()
  returnUrl?: string;
}