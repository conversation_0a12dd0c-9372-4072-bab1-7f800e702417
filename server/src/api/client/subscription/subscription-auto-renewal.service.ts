import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service'
import { Subscriptions } from '@/common/subscription/subscription.constants'
import { UserSubscriptions } from '@/entity/UserSubscriptions'
import { Injectable, Logger } from '@nestjs/common'
import { <PERSON>ron, CronExpression } from '@nestjs/schedule'
import { LessThan, Not } from 'typeorm'

@Injectable()
export class SubscriptionAutoRenewalService {
  private readonly logger = new Logger(SubscriptionAutoRenewalService.name);

  constructor(
    private readonly yookassaService: YookassaService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async processAutoRenewals() {
    return this.runAutoRenewals();
  }

  async runAutoRenewals() {
    this.logger.log('Начинаем обработку автооплат подписок');

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(23, 59, 59, 999);

    const expiringSubs = await UserSubscriptions.find({
      where: {
        isAutoRenew: true,
        provider: 'yookassa',
        currentPeriodEnd: LessThan(tomorrow),
        yookassaPaymentMethodId: Not(null),
      },
      relations: ['user'],
    });

    this.logger.log(`Найдено ${expiringSubs.length} подписок для автооплаты`);

    for (const subscription of expiringSubs) {
      try {
        await this.renewSubscription(subscription);
      } catch (error) {
        this.logger.error(`Ошибка при автооплате подписки ${subscription.id}:`, error);
      }
    }

    this.logger.log('Обработка автооплат завершена');
  }

  private async renewSubscription(subscription: UserSubscriptions) {
    const subscriptionConfig = Subscriptions[subscription.type];
    if (!subscriptionConfig) {
      this.logger.error(`Конфигурация подписки ${subscription.type} не найдена`);
      return;
    }

    const period = subscription.isYearly ? 'yearly' : 'monthly';
    const amount = subscriptionConfig.price[period].rub;
    const description = `Автооплата подписки ${subscriptionConfig.name} (${subscription.isYearly ? 'годовая' : 'месячная'}), пользователь ${subscription.user.firstName} ${subscription.user.lastName}`;

    const metadata = {
      module: 'subscriptions',
      value: subscription.type,
      userId: subscription.user.id,
      autoRenew: true,
      renewalFor: subscription.id,
      isYearly: subscription.isYearly,
    };

    try {
      const payment = await this.yookassaService.createRecurringPayment(
        amount,
        'RUB',
        description,
        subscription.yookassaPaymentMethodId,
        metadata
      );

      if (payment.status === 'succeeded') {
        const periodDays = subscription.isYearly ? 365 : 30;
        subscription.currentPeriodEnd = new Date(Date.now() + periodDays * 24 * 60 * 60 * 1000);
        subscription.paymentId = payment.id;
        await subscription.save();

        this.logger.log(`Автооплата подписки ${subscription.id} успешно выполнена`);
      } else {
        this.logger.warn(`Автооплата подписки ${subscription.id} не удалась. Статус: ${payment.status}`);
        
        if (payment.status === 'canceled') {
          subscription.isAutoRenew = false;
          await subscription.save();
          this.logger.log(`Автооплата отключена для подписки ${subscription.id}`);
        }
      }
    } catch (error) {
      this.logger.error(`Ошибка при автооплате подписки ${subscription.id}:`, error);
      
      subscription.isAutoRenew = false;
      await subscription.save();
      this.logger.log(`Автооплата отключена для подписки ${subscription.id} из-за ошибки`);
    }
  }
}
