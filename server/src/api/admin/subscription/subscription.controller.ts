import { Groups } from '@/api/user/decorators/groups.decorator'
import { AccessGuard } from '@/api/user/guards/access.guard'
import { JwtAuthGuard } from '@/api/user/guards/auth.guard'
import { Body, Controller, Delete, Param, Post, UseGuards } from '@nestjs/common'
import { SubscriptionService } from './subscription.service'

@Controller('admin/subscriptions')
@Groups('USER_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Post('user/:userId')
  async addSubscriptionToUser(
    @Param('userId') userId: number,
    @Body() body: { type: string; currentPeriodEnd: Date; isAutoRenew?: boolean }
  ) {
    return await this.subscriptionService.addSubscriptionToUser(userId, body);
  }

  @Delete(':subscriptionId')
  async removeSubscription(@Param('subscriptionId') subscriptionId: number) {
    return await this.subscriptionService.removeSubscription(subscriptionId);
  }
}
