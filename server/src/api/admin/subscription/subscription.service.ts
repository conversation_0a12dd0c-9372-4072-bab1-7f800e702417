import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { User } from '@/entity/User';
import { UserSubscriptions } from '@/entity/UserSubscriptions';
import { Subscriptions, SubscriptionType } from '@/common/subscription/subscription.constants';

@Injectable()
export class SubscriptionService {
  async addSubscriptionToUser(
    userId: number,
    data: { type: string; currentPeriodEnd: Date; isAutoRenew?: boolean }
  ) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['subscriptions']
    });

    if (!user) {
      throw new NotFoundException('Пользователь не найден');
    }

    if (!Subscriptions[data.type as SubscriptionType]) {
      throw new BadRequestException('Неверный тип подписки');
    }

    const existingSubscription = user.subscriptions.find(sub => sub.type === data.type);
    if (existingSubscription) {
      throw new BadRequestException('У пользователя уже есть такая подписка');
    }

    const subscription = new UserSubscriptions();
    subscription.type = data.type as SubscriptionType;
    subscription.paymentId = `admin-${Date.now()}`;
    subscription.currentPeriodEnd = new Date(data.currentPeriodEnd);
    subscription.isAutoRenew = data.isAutoRenew || false;
    subscription.provider = null;
    subscription.user = user;

    await subscription.save();

    return { success: true, subscription };
  }

  async removeSubscription(subscriptionId: number) {
    const subscription = await UserSubscriptions.findOne({
      where: { id: subscriptionId }
    });

    if (!subscription) {
      throw new NotFoundException('Подписка не найдена');
    }

    await subscription.remove();

    return { success: true };
  }
}
