import * as cheerio from 'cheerio'
import * as epubParser from "epub-parser"
import { writeFileSync } from 'fs'
import { basename, join } from 'path'

export class EpubParser {
  private path: string = null
  private imageDir: string = './upload/epub/images'

  constructor(path: string) {
    this.path = path;
  }

  parse() {
    return new Promise((resolve, reject) => {
      epubParser.open(this.path, (err, data) => {
        if(err) return reject(err);

        const chapters = this.getChapters(data);
        resolve(chapters);
      })
    })
  }

  getChapters(data) {
    const baseUrl = process.env.BASE_URL || 'http://localhost:9015'
    const chapters = [];
    const navHtml = !data.easy.isEpub3 ? data.easy.navMapHTML : data.easy.epub3NavHtml;
    const $nav = cheerio.load(navHtml);
    const _this = this;

    const nav = {};
    $nav('a').each(function() {
      const href = $nav(this).attr('href').split('#')[0];
      nav[href] = $nav(this).text();
    })

    for(let item of Object.values(data.easy.linearSpine).filter((e: any) => e.item.$['media-type'] == 'application/xhtml+xml') as any) {
      const title = nav[item.item.$.href] || 'Без названия';
      const content = _this.getContent(data.paths.opsRoot + item.item.$.href);
      const $ = cheerio.load(content, {xmlMode: true, xml: {decodeEntities: false}});

      $('img, image').each((index, element) => {
        const image = $(element);
        const tagName = element.tagName.toLowerCase();

        const xlinkHref = image.attr('xlink:href');
        const href = image.attr('href');
        const srcAttr = image.attr('src');

        const finalSrc = xlinkHref || href || srcAttr;

        const fileName = basename(finalSrc);
        const imagePath = join(_this.imageDir, fileName);

        image.attr(srcAttr ? 'src' : 'xlink:href', `${baseUrl}/${imagePath}`);
      });

      chapters.push({ title, content: $.html()})
    }

    const imagesHref = Object.keys(data.easy.itemHashByHref).filter(e => /\.(png|jpg|jpeg)$/i.test(e));
    imagesHref.forEach(e => {
      const imagePath = join(_this.imageDir, basename(e));
      const binary = epubParser.extractBinary(data.paths.opsRoot + e)
      writeFileSync(imagePath, binary, 'binary');
    })

    return chapters;
  }

  getContent(href: string) {
    return epubParser.extractText(href)
  }
}