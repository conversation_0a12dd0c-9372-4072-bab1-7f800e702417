import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component"
import { ToasterService } from "@/services/toaster.service"
import { UserService } from "@/services/user.service"
import { CommonModule } from "@angular/common"
import { Component, ElementRef, inject, ViewChild } from '@angular/core'
import { FormBuilder, FormsModule, ReactiveFormsModule } from "@angular/forms"
import { Router } from "@angular/router"
import moment from 'moment'
import { CookieService } from 'ngx-cookie-service'
import { MultiSelectModule } from 'primeng/multiselect'
import { SelectModule } from 'primeng/select'
import { TableModule } from 'primeng/table'
import { environment } from "../../../environments/environment"
import { countriesData } from './user/countries.var'

@Component({
    selector: 'app-users',
    imports: [
        ReactiveFormsModule,
        CommonModule,
        FormsModule,
        TableModule,
        MultiSelectModule,
        SelectModule,
        AdminDialogComponent
    ],
    providers: [CookieService],
    templateUrl: './users.component.html',
    styleUrl: './users.component.scss'
})
export class UsersComponent {
  protected readonly environment = environment;
  @ViewChild('editDialog') editDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
  message: string = "";
  userService = inject(UserService)
  fb: FormBuilder = inject(FormBuilder)
  cookieService: CookieService = inject(CookieService)
  router = inject(Router)
  toasterService = inject(ToasterService)
  editForm = this.fb.group({
    id: null,
    firstName: null,
    lastName: null,
    middleName: null,
    spiritualName: null,
    email: null,
    active: true,
    confirmed: false,
    groups: [],
    statuses: [],
  })
  groups: any = []
  statuses: any = []
  searchTerm: string = "";
  groupQuery: string = "";
  statusQuery: string = "";
  confirmedQuery: string = "";
  countryQuery: string = "";
  activeQuery:string = "";
  subscriptionQuery:string = "";
  hasSubscriptionQuery: boolean = false;
  sort: string = 'id'
  countries = countriesData;

  selectedColumns =  [
    { field: 'avatar', header: 'Аватар' },
    { field: 'id', header: 'ID' },
    { field: 'email', header: 'E-mail' },
    { field: 'createdAt', header: 'Дата регистрации' },
    { field: 'firstName', header: 'Имя' },
    { field: 'lastName', header: 'Фамилия' },
    { field: 'spiritualName', header: 'Духовное имя' },
    { field: 'groups', header: 'Группа' },
    { field: 'statuses', header: 'Статус' },
    { field: 'lastActivity', header: 'Активность' },
    { field: 'confirmed', header: 'Подтвержден' },
    { field: 'active', header: 'Активен' },
  ];

  cols = [
    { field: 'avatar', header: 'Аватар' },
    { field: 'id', header: 'ID' },
    { field: 'email', header: 'E-mail' },
    { field: 'createdAt', header: 'Дата регистрации' },
    { field: 'firstName', header: 'Имя' },
    { field: 'lastName', header: 'Фамилия' },
    { field: 'middleName', header: 'Отчество' },
    { field: 'spiritualName', header: 'Духовное имя' },
    { field: 'groups', header: 'Группа' },
    { field: 'statuses', header: 'Статус' },
    { field: 'lastActivity', header: 'Активность последняя' },
    { field: 'confirmed', header: 'Подтвержден' },
    { field: 'active', header: 'Активен' },
    { field: 'comment', header: 'Комментарий' },
    { field: 'city', header: 'Город' },
    { field: 'country', header: 'Страна' },
    { field: 'birthDate', header: 'Дата рождения' },
    { field: 'health', header: 'Состояние здоровья' },
    { field: 'education', header: 'Образование' },
    { field: 'supportInstructor', header: 'Наставник' },
    { field: 'supportConsultation', header: 'Консультация' },
    { field: 'supportCorrespondence', header: 'Обучение' },
    { field: 'dpGoal', header: 'Цель в практике' },
  ]

  page: number = 1

  ngOnInit() {
    this.loadSelectedColumnsFromStorage();
    this.userService.getAll(this.page).subscribe()
    this.userService.getGroups().subscribe((res: any) => this.groups = res)
    this.userService.getStatuses().subscribe((res: any) => this.statuses = res)
  }

  get statusLabel() {
    return (arr: any) => arr.map((e: any) => this.statuses.find((k: any) => k.value === e)?.label)
  }

  get groupLabel() {
    return (arr: any) => arr.map((e: any) => this.groups.find((k: any) => k.value === e)?.label)
  }

get filteredUsers() {
  let users = this.userService.users.filter((e: any) => {
    return ((e.firstName && e.firstName.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.lastName && e.lastName.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.middleName && e.middleName.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.spiritualName && e.spiritualName.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.comment && e.comment.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.skills && e.skills.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.dpPractice && e.dpPractice.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.dpLevel && e.dpLevel.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.dpEvents && e.dpEvents.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.dpMeet && e.dpMeet.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.speciality && e.speciality.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.city && e.city.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.service && e.service.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) ||
        (e.email && e.email.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1)) &&
        (this.groupQuery ? e.groups && e.groups.includes(this.groupQuery) : true) &&
        (this.statusQuery ? e.statuses && e.statuses.includes(this.statusQuery) : true) &&
        (this.activeQuery ? e.active === (this.activeQuery === 'true') : true) &&
        (this.confirmedQuery ? e.confirmed === (this.confirmedQuery === 'true') : true) &&
        (this.countryQuery ? (e.country && e.country.toLowerCase() === this.countryQuery.toLowerCase()) : true) &&
        (this.subscriptionQuery ? e.subscriptions.length && e.subscriptions.some((s: any) => s.type === this.subscriptionQuery) : true) &&
        (this.hasSubscriptionQuery ? e.subscriptions && e.subscriptions.length > 0 : true)
  })
  return users
}

  openDialog(user: any) {
    this.editForm.patchValue(user);
    this.editDialog.nativeElement.showModal()
  }

  closeDialog() {
    this.editForm.reset()
    this.editDialog.nativeElement.close()
  }

  saveUser() {
    const data = this.editForm.value;
    this.userService.updateUser(data).subscribe(() => {
      this.toasterService.showSuccess('Пользователь успешно обновлен');
      this.closeDialog();
      this.userService.getAll(this.page).subscribe()
    })
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
    this.closeDialog();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    return this.adminDialog.showConfirm(message);
  }

  deleteUser(id: number) {
    this.openConfirmationDialog('Удалить пользователя?').then((confirmed) => {
      if (confirmed) {
        this.userService.deleteUser(id).subscribe({
          next: () => {
            this.toasterService.showSuccess('Пользователь успешно удален');
            this.userService.getAll(this.page).subscribe()
          },
          error: () => {
            this.toasterService.showError('Ошибка удаления, попробуйте еще раз');
            this.userService.getAll(this.page).subscribe()
          }
        });
      }
    });
  }

  formatDate(date: string | Date): string {
    return moment(date).format('DD.MM.YYYY');
  }

  findCountryByIsoCode(isoCode: string): any {
    if (!isoCode) return null;
    return this.countries.find(country => country.iso_code2 === isoCode);
  }

  private saveSelectedColumnsToStorage() {
    const selectedColumnFields = this.selectedColumns.map(col => col.field);
    this.cookieService.set('userSelectedColumns', JSON.stringify(selectedColumnFields), 30); // 30 дней срок хранения
  }

  private loadSelectedColumnsFromStorage() {
    const savedColumns = this.cookieService.get('userSelectedColumns');
    if (savedColumns) {
      try {
        const selectedColumnFields = JSON.parse(savedColumns);
        // Фильтруем колонки, чтобы включить только те, которые существуют в cols
        this.selectedColumns = this.cols.filter(col =>
          selectedColumnFields.includes(col.field)
        );

        // Если по какой-то причине нет колонок, используем дефолтные
        if (this.selectedColumns.length === 0) {
          this.resetToDefaultColumns();
        }
      } catch (e) {
        console.error('Ошибка при загрузке сохраненных колонок:', e);
        this.resetToDefaultColumns();
      }
    }
  }

  private resetToDefaultColumns() {
    // Установка колонок по умолчанию
    this.selectedColumns = [
      { field: 'id', header: 'ID' },
      { field: 'email', header: 'E-mail' },
      { field: 'createdAt', header: 'Дата регистрации' },
      { field: 'firstName', header: 'Имя' },
      { field: 'lastName', header: 'Фамилия' },
      { field: 'spiritualName', header: 'Духовное имя' },
      { field: 'groups', header: 'Группа' },
      { field: 'statuses', header: 'Статус' },
      { field: 'lastActivity', header: 'Активность' },
      { field: 'confirmed', header: 'Подтвержден' },
      { field: 'active', header: 'Активен' },
    ];
  }

  onColumnSelectionChange() {
    this.saveSelectedColumnsToStorage();
  }
}
