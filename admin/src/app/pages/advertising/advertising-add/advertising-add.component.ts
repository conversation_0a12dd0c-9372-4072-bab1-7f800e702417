import { PhotopreviewComponent } from '@/components/photopreview/photopreview.component'
import { AdvertisingService } from '@/services/advertising.service'
import { FileService } from '@/services/file.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, Location } from '@angular/common'
import { Component, ElementRef, inject, OnInit, ViewChild } from '@angular/core'
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { environment } from '../../../../environments/environment'

@Component({
  selector: 'app-advertising-add',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PhotopreviewComponent
  ],
  templateUrl: './advertising-add.component.html',
  styleUrl: './advertising-add.component.scss'
})
export class AdvertisingAddComponent implements OnInit {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  
  fb = inject(FormBuilder);
  route = inject(ActivatedRoute);
  router = inject(Router);
  advertisingService = inject(AdvertisingService);
  fileService = inject(FileService);
  toasterService = inject(ToasterService);
  location = inject(Location);

  languages = ['ru', 'en', 'de', 'ua', 'it'];
  selectedLanguage = 'ru';
  addAdvertisingForms: { lang: string, form: any }[] = [];
  slug = this.route.snapshot.params['slug'];
  message: string = '';
  protected readonly environment = environment;

  ngOnInit() {
    this.fillForm();
    
    if (this.slug && this.slug !== 'add') {
      this.advertisingService.getBySlug(this.slug).subscribe((res: any) => {
        res.forEach((item: any) => {
          const formIndex = this.addAdvertisingForms.findIndex(f => f.lang === item.lang);
          if (formIndex !== -1) {
            this.addAdvertisingForms[formIndex].form.patchValue({
              active: item.active,
              advertising: item.advertising,
              title: item.title,
              description: item.description,
              link: item.link,
              type: item.type,
              freq: item.freq,
              date: item.date,
              image: item.image
            });
          }
        });
      });
    }
  }

  fillForm() {
    this.languages.forEach(lang => {
      this.addAdvertisingForms.push({
        lang,
        form: this.fb.group({
          active: [true],
          advertising: [false],
          title: ['', Validators.required],
          description: ['', Validators.required],
          link: ['', Validators.required],
          type: [1],
          freq: [1],
          date: [null],
          image: [null, Validators.required]
        })
      });
    });
  }

  selectLanguage(lang: string) {
    this.selectedLanguage = lang;
  }

  uploadFile(e: Event) {
    const files = (e.target as HTMLInputElement).files!
    if (files.length) {
      this.fileService.uploadToTempFolder(files).subscribe((res: any) => {
        const currentForm = this.addAdvertisingForms.find(f => f.lang === this.selectedLanguage);
        if (currentForm) {
          currentForm.form.patchValue({ image: res[0] });
        }
      });
    }
  }

  addAdvertisingFormSubmit() {
    const form = this.addAdvertisingForms.map(e => ({ ...e, form: e.form.value }));
    
    if (this.slug && this.slug !== 'add') {
      return this.advertisingService.update(this.slug, form).subscribe({
        next: () => {
          this.toasterService.showSuccess('Новость успешно обновлена');
          this.back();
        },
        error: () => {
          this.toasterService.showError('Новость не обновлена. Попробуйте еще');
        }
      });
    }

    return this.advertisingService.create(form).subscribe({
      next: (res: any) => {
        this.toasterService.showSuccess('Новость успешно добавлена');
        this.router.navigateByUrl('/advertising/' + res.slug);
      },
      error: () => {
        this.toasterService.showError('Новость не добавлена. Попробуйте еще');
      }
    });
  }

  addValidationFormSubmit() {
    const currentForm = this.addAdvertisingForms.find(f => f.lang === this.selectedLanguage);
    return currentForm?.form.valid || false;
  }

  back() {
    this.location.back();
  }

  close(dialog: any) {
    dialog.close();
  }

  openDialog(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal();
  }
}
