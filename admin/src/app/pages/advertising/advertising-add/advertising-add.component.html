<div class="panel" style='margin-bottom: 100px;'>
  <div class="flex items-center justify-between mb-5">
    <h5 class="font-semibold text-lg dark:text-white-light">
      {{slug && slug !== 'add' ? 'Редактировать новость' : 'Добавить новость'}}
    </h5>
    <button (click)="back()" class="btn btn-outline-secondary">Назад</button>
  </div>

  <div class="mb-5">
    <div class="mt-3 flex flex-wrap border-b border-white-light dark:border-[#191e3a]">
      @for(lang of languages; track lang) {
        <a
          href="javascript:"
          class="-mb-[1px] block border border-transparent p-3.5 py-2 !outline-none transition duration-300 hover:text-primary dark:hover:border-b-black"
          [ngClass]="{ '!border-white-light !border-b-white  text-primary dark:!border-[#191e3a] dark:!border-b-black': selectedLanguage === lang }"
          (click)="selectLanguage(lang)"
        >
          {{lang.toUpperCase()}}
        </a>
      }
    </div>
  </div>

  @for(item of addAdvertisingForms; track item.lang) {
    @if(selectedLanguage == item.lang) {
      <dialog #dialog class="admin-modal">
        <div class="admin-modal-content">
          <div>{{message}}</div>
          <div class="admin-modal-footer">
            <button (click)="close(dialog)" class="btn btn-primary">OK</button>
          </div>
        </div>
      </dialog>
      
      <form [formGroup]="item.form" class="space-y-5">
        <div class="grid grid-cols-3 gap-4">
          <div>
            <label><input type="checkbox" class="form-checkbox" formControlName="active">Календарь</label>
          </div>
          <div>
            <label><input type="checkbox" class="form-checkbox" formControlName="advertising">Реклама</label>
          </div>
        </div>

        <div>
          <label>Заголовок</label>
          <input type="text" class="form-input" formControlName="title" required>
        </div>

        <div>
          <label>Описание</label>
          <textarea formControlName="description" class="form-textarea" required></textarea>
        </div>

        <div>
          <label>Ссылка</label>
          <input type="text" class="form-input" formControlName="link" required>
        </div>

        <div>
          <label>Тип</label>
          <select formControlName="type" class="form-select">
            <option value="1">Модалка</option>
            <option value="2">Баннер</option>
          </select>
        </div>

        <div>
          <label>Количество показов</label>
          <input type="number" class="form-input" formControlName="freq" min="1">
        </div>

        <div>
          <label>Дата</label>
          <input type="date" formControlName="date" class="form-input">
        </div>

        <div>
          <label>Изображение</label>
          <input type="file" class="form-input" accept="image/*" (change)="uploadFile($event)" required>
          @if(item.form.value.image) {
            <PhotoPreview [disableRemoveBtn]="true" [items]="[item.form.value.image]" [hasDescription]="false" (onItemRemoved)="item.form.patchValue({image: null})"></PhotoPreview>
          }
        </div>
      </form>
    }
  }

  <div class="fixed-buttons">
    @if(slug && slug !== 'add') {
      <button [disabled]="!addValidationFormSubmit()" (click)="addAdvertisingFormSubmit()" type="submit" class="btn btn-primary">Обновить</button>
      <button (click)="back()" class="btn">Закрыть</button>
    } @else {
      <button [disabled]="!addValidationFormSubmit()" (click)="addAdvertisingFormSubmit()" type="submit" class="btn btn-primary">Создать</button>
      <button (click)="back()" class="btn">Закрыть</button>
    }
  </div>
</div>
