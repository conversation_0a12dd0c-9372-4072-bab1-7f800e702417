<div class="panel">
  <div class="flex items-center justify-between mb-5">
    <h5 class="font-semibold text-lg dark:text-white-light">Новости</h5>
    <a routerLink="/advertising/add" class="btn btn-primary">Добавить новость</a>
  </div>

  <div class="table-responsive">
    <table>
      <thead>
        <tr>
          <th>Изображение</th>
          <th>Заголовок</th>
          <th>Описание</th>
          <th>Действия</th>
        </tr>
      </thead>
      <tbody>
        @for(item of advertisingService.list(); track item.id) {
          <tr>
            <td>
              @if(item.image) {
                <img [src]="environment.serverUrl + '/upload/' + item.image.name"
                     alt="Preview" class="w-12 h-12 object-cover rounded">
              } @else {
                <div class="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                  <span class="text-gray-400 text-xs">Нет фото</span>
                </div>
              }
            </td>
            <td>{{item.title}}</td>
            <td class="max-w-xs truncate" [title]="item.description">{{item.description}}</td>
            <td>
              <div class="admin-table-actions">
                <a [routerLink]="'/advertising/' + item.slug" class="btn btn-sm btn-primary">Редактировать</a>
                <button class="btn btn-sm btn-danger" (click)="deleteAdvertising(item.slug)">Удалить</button>
              </div>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
</div>


