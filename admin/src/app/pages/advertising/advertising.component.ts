import { AdvertisingService } from "@/services/advertising.service"
import { CommonModule } from '@angular/common'
import { Component, inject, OnInit } from '@angular/core'
import { RouterModule } from '@angular/router'
import { environment } from "../../../environments/environment"

@Component({
    selector: 'app-advertising',
    standalone: true,
    imports: [
        CommonModule,
        RouterModule
    ],
    templateUrl: './advertising.component.html',
    styleUrl: './advertising.component.scss'
})
export class AdvertisingComponent implements OnInit {
  advertisingService = inject(AdvertisingService);
  protected readonly environment = environment;

  ngOnInit() {
    this.advertisingService.getAll().subscribe();
  }

  deleteAdvertising(slug: string) {
    if (confirm('Вы уверены, что хотите удалить эту новость?')) {
      this.advertisingService.delete(slug).subscribe(() => {
        this.advertisingService.getAll().subscribe();
      });
    }
  }
}
