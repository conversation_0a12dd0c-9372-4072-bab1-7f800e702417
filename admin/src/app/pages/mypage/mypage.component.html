<div class="admin-component">
  <!-- Page Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Личные страницы</h1>
    </div>
    <div class="admin-actions">
      <button class="btn btn-primary" routerLink="/mypage/add">Создать страницу</button>
    </div>
  </div>

  <!-- Modals -->
  <dialog #modal class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(modal)" class="btn btn-primary">Да</button>
      </div>
    </div>
  </dialog>

  <!-- Admin Dialog Component -->
  <admin-dialog></admin-dialog>

  <dialog #confirmDialog class="admin-modal">
    <div class="admin-modal-content">
      <div>{{ message }}</div>
      <div class="admin-modal-footer">
        <button class="btn btn-danger">Да</button>
        <button class="btn btn-outline-secondary">Отмена</button>
      </div>
    </div>
  </dialog>

  <div class="admin-filters mb-4">
    <div class="flex gap-4 items-center">
      <div class="flex items-center">
        <input
          type="checkbox"
          id="onlyMy"
          [checked]="filters.onlyMy"
          (change)="onMyFilterChange($event)"
          class="mr-2">
        <label for="onlyMy">Только мои</label>
      </div>

      <div class="flex items-center">
        <label for="authorSelect" class="mr-2">Автор:</label>
        <select
          id="authorSelect"
          [value]="filters.authorId || ''"
          (change)="onAuthorFilterChange($event)"
          class="form-select">
          <option value="">Все авторы</option>
          @for(user of users; track user.id) {
            <option [value]="user.id">{{getUserDisplayName(user)}}</option>
          }
        </select>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="admin-content-wrapper">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>Название</th>
            <th>Описание</th>
            <th>Автор</th>
            <th class="text-center w-64">Действия</th>
          </tr>
        </thead>
        <tbody>
          @for(item of items; track item.id) {
            <tr>
              <td>{{item.title}}</td>
              <td>{{item.description}}</td>
              <td>{{item.author ? getUserDisplayName(item.author) : 'Не указан'}}</td>
              <td>
                <div class="admin-table-actions">
                  <button class="btn btn-sm btn-primary" (click)="router.navigate(['/mypage/' + item.id])">Редактировать</button>
                  <button class="btn btn-sm btn-danger" (click)="removePage(item.id)">Удалить</button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
