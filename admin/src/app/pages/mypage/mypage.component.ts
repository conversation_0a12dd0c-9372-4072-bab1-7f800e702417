import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component"
import { MypageService } from "@/services/mypage.service"
import { ToasterService } from "@/services/toaster.service"
import { Component, ElementRef, inject, ViewChild } from '@angular/core'
import { Router, RouterLink } from "@angular/router"

@Component({
    selector: 'app-mypage',
    standalone: true,
    imports: [
        RouterLink,
        AdminDialogComponent
    ],
    templateUrl: './mypage.component.html',
    styleUrl: './mypage.component.scss'
})
export class MypageComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
  message: string = "";
  mypageService = inject(MypageService);
  router = inject(Router)
  toasterService = inject(ToasterService);
  items: any = []
  users: any = []
  filters: any = {
    onlyMy: false,
    authorId: null as number | null
  }

  ngOnInit(){
    this.getAll()
    this.getUsers()
  }

  getAll() {
    this.mypageService.getAll(this.filters).subscribe((res: any) => this.items = res)
  }

  getUsers() {
    this.mypageService.getUsers().subscribe((res: any) => this.users = res)
  }

  onMyFilterChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.filters.onlyMy = target.checked;
    if (this.filters.onlyMy) {
      this.filters.authorId = null;
    }
    this.getAll();
  }

  onAuthorFilterChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    this.filters.authorId = target.value ? Number(target.value) : null;
    if (this.filters.authorId) {
      this.filters.onlyMy = false;
    }
    this.getAll();
  }

  getUserDisplayName(user: any): string {
    return user.spiritualName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email;
  }
  
  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    return this.adminDialog.showConfirm(message);
  }

  removePage(id: number) {
    this.openConfirmationDialog('Удалить страницу?').then((confirmed) => {
      if (confirmed) {
        this.mypageService.removePage(id).subscribe({
          next: () => {
            this.toasterService.showSuccess('Страница успешно удалена');
            this.getAll()
          },
          error: () => {
            this.toasterService.showError('Ошибка удаления, попробуйте еще раз');
            this.getAll()
          }
        });
      }
    });
  }
}
