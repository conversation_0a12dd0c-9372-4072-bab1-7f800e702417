import { CarouselComponent } from "@/components/carousel/carousel.component";
import { PhotopreviewComponent } from "@/components/photopreview/photopreview.component";
import editorConfig from "@/editor.config";
import { LanguagesEnum } from "@/enums/languages.enum";
import { FileService } from "@/services/file.service";
import { MypageService } from "@/services/mypage.service";
import { CommonModule } from "@angular/common";
import { Component, ElementRef, inject, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { CKEditorModule } from "@ckeditor/ckeditor5-angular";
import { ClassicEditor } from "ckeditor5";
import { environment } from "../../../../environments/environment";
import { CustomUploadAdapter } from './custom-upload-adapter';

@Component({
    selector: 'app-constructor-add',
    imports: [
        ReactiveFormsModule,
        CommonModule,
        PhotopreviewComponent,
        CKEditorModule,
        CarouselComponent
    ],
    templateUrl: './mypage-add.component.html',
    styleUrl: './mypage-add.component.scss',
    encapsulation: ViewEncapsulation.None
})
export class MypageAddComponent {
  @ViewChild('addButtonDialog') addButtonDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('carouselFormDialog') carouselFormDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('carouselItemFormDialog') carouselItemFormDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('carouselItemImage') carouselItemImage!: ElementRef<HTMLInputElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild(CarouselComponent) carousel!: CarouselComponent;
  message: string = "";
  fb = inject(FormBuilder)
  router = inject(Router)
  route = inject(ActivatedRoute)
  fileService = inject(FileService)
  mypageService = inject(MypageService)
  languages = Object.keys(LanguagesEnum)
  id: number | string | null = this.route.snapshot.params['id']
  selectedLanguage = 'ru'
  forms: { lang: string, form: FormGroup }[] = []
  formFields: any = {
    id: null,
    title: null,
    description: null,
    seo_title: null,
    seo_description: null,
    content: null,
    bg: [null, Validators.required],
    telegram: null,
    instagram: null,
    email: null,
    phone: null,
    image: null,
  }
  buttonForm = this.fb.group({
    index: null,
    name: null,
    link: null,
  })
  currentForm: any = null
  editorInstance: any
  editorConfig: any = null
  isLayoutReady = false;

  ngOnInit() {
    this.initForms()
    this.currentForm = this.forms.find((e: any) => e.lang == this.selectedLanguage)!.form
    this.editorConfig = editorConfig

    if(this.id && this.id !== 'add') {
      this.mypageService.getOne(this.id).subscribe(this.patchForm.bind(this))
    }
  }

  ngAfterViewInit() {
    this.isLayoutReady = true;
  }

  onEditorReady(editor: any) {
    this.editorInstance = editor;
    this.editorInstance.plugins.get("FileRepository").createUploadAdapter = (loader: any) => {
      return new CustomUploadAdapter(loader, `${environment.apiUrl}/file/ck/upload`);
    };
  }

  get buttons() {
    return this.currentForm.get('buttons') as FormArray
  }

  get carouselItems() {
    return this.currentForm.get('carousel') as FormArray
  }

  initForms() {
    this.languages.forEach(lang => {
      this.forms.push({
        lang,
        form: this.fb.group({
          ...this.formFields,
          buttons: this.fb.array([]),
          carousel: this.fb.array([])
        })
      })
    })
  }

  selectLanguage(lang: string) {
    this.selectedLanguage = lang
    this.currentForm = this.forms.find((e: any) => e.lang == this.selectedLanguage)!.form;
  }


  showAddButtonForm() {
    this.addButtonDialog.nativeElement.showModal()
  }

  closeAddButtonForm() {
    this.addButtonDialog.nativeElement.close()
    this.buttonForm.reset()
  }

  addButton() {
    const buttons = this.currentForm.get('buttons')
    if(this.buttonForm.value.index !== null) {
      buttons.at(this.buttonForm.value.index).patchValue(this.buttonForm.value)
    } else {
      buttons.push(this.fb.group(this.buttonForm.value))
    }
    this.closeAddButtonForm()
  }

  editButton(index: number, button: any) {
    button.index = index
    this.buttonForm.patchValue(button)
    this.showAddButtonForm()
  }

  removeButton(index: number) {
    this.currentForm.get('buttons').removeAt(index)
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
    this.router.navigate(['/mypage'])
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  uploadBackground(e: Event) {
    const target = e.target as HTMLInputElement;
    if(!target.files) return

    this.fileService.uploadToTempFolder(target.files).subscribe((res: any) => {
      this.currentForm.controls.bg.patchValue(res[0])
    })
  }

  uploadImage(e: Event) {
    const target = e.target as HTMLInputElement;
    if(!target.files) return

    this.fileService.uploadToTempFolder(target.files).subscribe((res: any) => {
      this.currentForm.controls.image.patchValue(res[0])
    })
  }

  onImageRemoved() {
    this.currentForm.controls.image.patchValue(null)
  }

  onSubmit() {
    const data = []
    for(let i in this.forms) {
      if(!this.forms[i].form.value.title) continue
      data.push({
        lang: this.forms[i].lang,
        ...this.forms[i].form.value,
        buttons: (this.forms[i].form.get('buttons') as FormArray).value,
        carousel: (this.forms[i].form.get('carousel') as FormArray).getRawValue(),
      })
    }
    this.mypageService.create(data).subscribe(() => {
      this.forms.forEach(item => {
        item.form.reset()
        this.openModal('Успешно!')
      })
    })
  }

  patchForm(res: any) {
    for (let item of res) {
      const form = this.forms.find((e: any) => e.lang == item.lang)!;

      if (typeof item.bg === 'string') item.bg = JSON.parse(item.bg);

      form.form.patchValue(item);

      const buttonsArray: FormArray = this.fb.array([]);
      // buttonsArray.clear();
      if(item.buttons) {
        for (let button of item.buttons) {
          buttonsArray.push(this.fb.group(button));
        }
        form.form.setControl('buttons', buttonsArray);
      }

      if(item.carousel) {
        const carouselArray: FormArray = this.fb.array([]);
        //carouselArray.clear();
        for (let carouselItem of item.carousel) {
          const itemsArray: FormArray = this.fb.array([]);
          for (let item of carouselItem.items) {
            itemsArray.push(this.fb.group(item));
          }
          const newCarouselGroup = this.fb.group({
            ...carouselItem,
            items: itemsArray,
          });
          carouselArray.push(newCarouselGroup);
        }
        form.form.setControl('carousel', carouselArray);
      }

    }
  }

  onBackgroundRemoved() {
    this.currentForm.controls.bg.patchValue(null)
  }

  editCarousel(i: number, item: any) {
    item.index = i;

    const itemsArray: FormArray = this.fb.array([]);
    for (let itemElement of item.items) {
      itemsArray.push(this.fb.group(itemElement));
    }

    this.carousel.carouselForm.patchValue({...item, items: []});
    this.carousel.carouselForm.setControl('items', itemsArray);
    this.carousel.showCarouselForm();
  }

  removeCarousel(i: number, form: FormGroup) {
    (form.get('carousel') as FormArray).removeAt(i)
  }

  followLink() {
    window.open(this.environment.clientUrl + 'ru' + '/mypage/' + this.id)
  }

  copyLink() {
    window.navigator.clipboard.writeText(`${this.environment.clientUrl}ru/mypage/${this.id}`)
  }

  onCarouselAdded(form: FormGroup) {
    const items = this.fb.array([]) as FormArray;
    for(let item of form.value.items) {
      items.push(this.fb.group(item))
    }
    const carousel = this.fb.group({
      ...form.value,
      items
    })
    this.carouselItems.push(carousel);
  }

  onCarouselEdited({ index, form }: { index: number, form: FormGroup }) {
    const updatedCarouselGroup = this.carouselItems.at(index) as FormGroup;

    // Обновляем простые поля (title, size и т.д.)
    updatedCarouselGroup.patchValue({
      index: form.value.index,
      title: form.value.title,
      size: form.value.size,
      type: form.value.type,
      // Не включаем items сюда, чтобы не конфликтовало с setControl
    });

    // Создаём новый FormArray для items
    const newItemsArray: FormArray = this.fb.array([]);
    for (const item of form.value.items) {
      newItemsArray.push(this.fb.group(item));
    }

    // Заменяем старый items новым массивом
    updatedCarouselGroup.setControl('items', newItemsArray);
  }

  protected readonly Editor = ClassicEditor;
  protected readonly FormControl = FormControl;
  protected readonly environment = environment;
}
