.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 6px;
    left: 40px;
    width: 100%;
    transform: translate(-50%, -4%);
    z-index: 12;
}

.spinner {
    width: 25px;
    height: 25px;
    border: 4px solid var(--selection);
    border-top: 4px solid var(--text-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

input[type="file"] {
    display: none;
}

.custom-file-upload {
    width: 100%;
    border-radius: 0.375rem;
    border-width: 1px;
    border-color: rgb(224 230 237);
    background-color: rgb(255 255 255);
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    height: 38px;
    cursor: pointer;
    position: relative;
}