import { Component, ElementRef, inject, ViewChild } from '@angular/core';
import { ConstructorService } from "@/services/constructor.service";
import {CarouselComponent} from "@/components/carousel/carousel.component";
import {FormArray, FormBuilder, FormGroup} from "@angular/forms";
import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component";
import { ToasterService } from "@/services/toaster.service";

@Component({
    selector: 'app-constructor',
    imports: [CarouselComponent, AdminDialogComponent],
    templateUrl: './constructor.component.html',
    styleUrl: './constructor.component.scss'
})
export class ConstructorComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
  @ViewChild(CarouselComponent) carousel!: CarouselComponent;
  fb = inject(FormBuilder)
  form = this.fb.group({
    carousel: this.fb.array([])
  })
  deletingItemId: number | null = null;
  message: string = "";
  constructorService = inject(ConstructorService)
  toasterService = inject(ToasterService)

  ngOnInit() {
    this.getAll()
  }

  getAll() {
    this.carousels.clear()
    this.constructorService.getAll().subscribe(res => {
      for(let item of res) {
        this.carousels.push(this.fb.group({
          ...item,
          items: this.fb.array(item.items.map((item: any) => this.fb.group(item)))
        }));
      }
    })
  }

  get carousels() {
    return this.form.get("carousel") as FormArray
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  delete(id: number) {
    this.deletingItemId = id;
    this.openConfirmationDialog('Удалить лендинг?').then((confirmed) => {
      if (confirmed) {
        this.constructorService.deleteBlock(id).subscribe({
          next: () => {
            this.toasterService.showSuccess('Лендинг успешно удален');
            this.getAll()
          },
          error: () => this.toasterService.showError('Ошибка удаления, попробуйте еще раз')
        });
      }
    });
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    return this.adminDialog.showConfirm(message);
  }

  edit(i: number, item: any) {
    item.index = i;

    const itemsArray: FormArray = this.fb.array([]);
    for (let itemElement of item.items) {
      itemsArray.push(this.fb.group(itemElement));
    }

    this.carousel.carouselForm.patchValue({...item, items: []});
    this.carousel.carouselForm.setControl('items', itemsArray);
    this.carousel.showCarouselForm();
  }

  onCarouselAdded(form: FormGroup) {
    this.constructorService.create(form.value).subscribe(() => this.getAll())
  }

  onCarouselEdited({index, form}: {index: number, form: FormGroup}) {
    this.constructorService.create({id: this.carousels.at(index).value.id, ...form.value}).subscribe(() => this.getAll())
  }
}
