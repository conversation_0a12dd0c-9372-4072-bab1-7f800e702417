import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component";
import { ToasterService } from "@/services/toaster.service";
import { TranslationService } from "@/services/translation.service";
import { Component, ElementRef, inject, ViewChild } from '@angular/core';
import { Router, RouterLink } from "@angular/router";

@Component({
    selector: 'app-lang',
    standalone: true,
    imports: [RouterLink, AdminDialogComponent],
    templateUrl: './translations.component.html',
    styleUrl: './translations.component.scss'
})
export class TranslationsComponent {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
  message: string = '';
  translationService = inject(TranslationService)
  router = inject(Router)
  toasterService = inject(ToasterService)
  ngOnInit() {
    this.translationService.getTranslations().subscribe()
  }

  delete(code: string) {
    this.adminDialog.showConfirm('Вы уверены, что хотите удалить этот перевод?').then((confirmed) => {
      if (confirmed) {
        this.translationService.delete(code).subscribe(() => {
          this.toasterService.showSuccess('Перевод успешно удален');
        });
      }
    });
  }

  openModal(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal();
  }

  exportTranslations() {
    this.translationService.exportTranslations().subscribe((blob: Blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'translations.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      this.toasterService.showSuccess('Переводы успешно экспортированы');
    });
  }

  importTranslations(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.translationService.importTranslations(file).subscribe({
        next: (response: any) => {
          this.toasterService.showSuccess('Переводы успешно импортированы');
          event.target.value = '';
        },
        error: (error) => {
          this.toasterService.showError('Ошибка при импорте переводов');
          event.target.value = '';
        }
      });
    }
  }

  closeModal(dialog: HTMLDialogElement) {
    dialog.close();
  }
}
