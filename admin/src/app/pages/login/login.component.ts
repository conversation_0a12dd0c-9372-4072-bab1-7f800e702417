import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component"
import { AuthService } from "@/services/auth.service"
import { Component, inject, ViewChild } from '@angular/core'
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms"
import { Router } from '@angular/router'
import { ToasterService } from "@/services/toaster.service"

@Component({
    selector: 'app-root',
    imports: [ReactiveFormsModule, AdminDialogComponent],
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss'
})
export class LoginComponent {
    @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
    router = inject(Router)
    authService = inject(AuthService);
    toasterService = inject(ToasterService)
    form = new FormGroup({
        email: new FormControl('', Validators.required),
        password: new FormControl('', Validators.required)
    })

    onSubmit() {
        this.authService.login(this.form.value).subscribe({
            next: () => this.router.navigate(['/']),
            error: () => {
                this.toasterService.showError('Неверный логин или пароль')
            }
        })
        return false;
    }
}
