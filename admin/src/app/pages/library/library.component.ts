import { Component, ElementRef, inject, ViewChild } from '@angular/core';
import {Router, ActivatedRoute} from "@angular/router";
import { LibraryService } from "@/services/library.service";
import { environment } from "../../../environments/environment";
import { TableModule } from 'primeng/table';
import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component";
import { ToasterService } from "@/services/toaster.service";

@Component({
    selector: 'app-library',
    imports: [TableModule, AdminDialogComponent],
    templateUrl: './library.component.html',
    styleUrl: './library.component.scss'
})
export class LibraryComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild('confirmModal') confirmModal!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
  deletingItemId: number | null = null;
  message: string = "";
  router = inject(Router)
  libraryService = inject(LibraryService)
  toasterService = inject(ToasterService)
  protected readonly environment = environment;

  selectedColumns =  [
    { field: 'ru', header: 'RU' },
    { field: 'en', header: 'EN' },
    { field: 'de', header: 'DE' },
    { field: 'ua', header: 'UA' },
    { field: 'it', header: 'IT' },
    { field: 'author', header: 'Автор' },
    { field: 'reader', header: 'Чтец' },
  ];

  cols = [
    { field: 'ru', header: 'RU' },
    { field: 'en', header: 'EN' },
    { field: 'de', header: 'DE' },
    { field: 'ua', header: 'UA' },
    { field: 'it', header: 'IT' },
    { field: 'author', header: 'Автор' },
    { field: 'reader', header: 'Чтец' },
  ]

  ngOnInit() {
    this.libraryService.getAll().subscribe()
  }

  openModal(modal: string, message: string) {
    this.message = message;
    modal == 'modal' ? this.modal.nativeElement.showModal() : this.confirmModal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  delete(id: number) {
    this.adminDialog.showConfirm('Вы уверены, что хотите удалить этот элемент?').then((confirmed) => {
      if (confirmed) {
        this.libraryService.delete(id).subscribe({
          next: () => {
            this.libraryService.getAll().subscribe();
            this.toasterService.showSuccess('Элемент успешно удален');
          },
          error: (err) => {
            console.error('Ошибка при удалении:', err);
            this.toasterService.showError('Ошибка при удалении элемента');
          },
        });
      }
    });
  }

  import(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) {
      this.openModal('modal', 'Ошибка при импорте');
      return;
    }
    const file = input.files[0];
    const formData = new FormData();
    formData.append('file', file);
    this.libraryService.importFromFile(formData).subscribe({
      next: () => {
        this.libraryService.getAll().subscribe();
        this.toasterService.showSuccess('Операция выполнена успешно!');
      },
      error: (err) => {
        this.toasterService.showError('Ошибка при импорте!');
        console.error('Ошибка при импорте:', err);
      },
    });
    input.value = '';
  }

  confirmDelete(confirm: boolean, modal: HTMLDialogElement) {
    if (confirm && this.deletingItemId) {
      this.libraryService.delete(this.deletingItemId).subscribe({
        next: () => {
          this.libraryService.getAll().subscribe();
          this.closeModal(modal);
        },
        error: (err) => {
          console.error('Ошибка при удалении:', err);
        },
      });
    } else {
      this.closeModal(modal);
    }
  }

  getTitle(lang: string, item: any) {
    return item.find((e: any) => e.lang === lang)?.title;
  }
}
