import { ForumService } from "@/services/forum.service"
import { Component, ElementRef, inject, ViewChild } from '@angular/core'
import { Router, RouterLink } from "@angular/router"
import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component"
import { ToasterService } from "@/services/toaster.service"

@Component({
    selector: 'app-forum-categories',
    imports: [
        RouterLink,
        AdminDialogComponent
    ],
    templateUrl: './forum-categories.component.html',
    styleUrl: './forum-categories.component.scss'
})
export class ForumCategoriesComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
  message: string = "";
  forumService = inject(ForumService);
  router = inject(Router)
  toasterService = inject(ToasterService);
  categories: any = []

  get numCategoryComments() {
    return (topics: any) => topics.reduce((acc: any, cur: any) => acc + cur.comments.length, 0)
  }

  ngOnInit() {
    this.getCategories();
  }

  getCategories() {
    this.forumService.getCategories().subscribe((res: any) => this.categories = res);
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    return this.adminDialog.showConfirm(message);
  }

  deleteCategory(id: number) {
    this.openConfirmationDialog('Удалить категорию?').then((confirmed) => {
      if (confirmed) {
        this.forumService.deleteCategory(id).subscribe({
          next: () => {
            this.toasterService.showSuccess('Категория успешно удалена');
            this.getCategories()
          },
          error: () => {
            this.toasterService.showError('Ошибка удаления, попробуйте еще раз');
            this.getCategories()
          }
        });
      }
    });
  }

  moveCategory(direction: 'up' | 'down', index: number) {
    const categories = [...this.categories];

    const itemToMove = categories[index];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    if (newIndex < 0 || newIndex >= categories.length) {
      return;
    }

    categories.splice(index, 1);
    categories.splice(newIndex, 0, itemToMove);

    this.categories = categories;

    const orderPayload = categories.map((cat, order) => ({
      id: cat.id,
      order
    }));

    this.forumService.updateCategoryOrder(orderPayload).subscribe();
  }
}
