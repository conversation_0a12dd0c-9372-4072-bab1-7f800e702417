import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {Router, RouterLink} from "@angular/router";
import {PhotoService} from "@/services/photo.service";
import {environment} from "../../../environments/environment";
import { ToasterService } from "@/services/toaster.service";

@Component({
    selector: 'app-photo',
    imports: [
        RouterLink
    ],
    templateUrl: './photo.component.html',
    styleUrl: './photo.component.scss'
})
export class PhotoComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  deletingItemId: number | null = null;
  message: string = "";
  photoService = inject(PhotoService)
  router = inject(Router)
  toasterService = inject(ToasterService)
  protected readonly environment = environment;
  ngOnInit() {
    this.photoService.getAll().subscribe()
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  delete(id: number) {
    this.deletingItemId = id;
    this.openConfirmationDialog('Удалить фотогалерею?').then((confirmed) => {
      if (confirmed) {
        this.photoService.delete(id).subscribe({
          next: () => this.toasterService.showSuccess('Фотогалерея успешно удалена'),
          error: () => this.toasterService.showError('Ошибка удаления, попробуйте еще раз')
        });
      }
    });
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  open(item: any) {
    window.open(this.environment.clientUrl + 'ru' + '/photo/' + item.slug)
  }
}
