import { HttpClient } from "@angular/common/http";
import { inject, Injectable, signal } from '@angular/core';
import { FormGroup } from "@angular/forms";
import { tap } from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  http = inject(HttpClient)
  translations = signal<{id: number, code: string, translations: {lang: string, text: string}[]}[] | null>(null)

  getTranslations() {
    return this.http.get(`/admin/translation`).pipe(
      tap((res: any) => this.translations.set(res))
    )
  }

  addTranslation(form: FormGroup) {
    return this.http.post(`/admin/translation/add`, form.value)
  }

  getOne(id: number) {
    return this.http.get(`/admin/translation/${id}`)
  }

  update(id: number, form: FormGroup) {
    return this.http.patch(`/admin/translation/${id}`, form.value)
  }

  delete(code: string) {
    return this.http.delete(`/admin/translation/${code}`).pipe(tap(() => this.getTranslations().subscribe()))
  }

  exportTranslations() {
    return this.http.get(`/admin/translation/export`, { responseType: 'blob' })
  }

  importTranslations(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    return this.http.post(`/admin/translation/import`, formData).pipe(
      tap(() => this.getTranslations().subscribe())
    )
  }
}
