import { HttpClient } from "@angular/common/http";
import { inject, Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class MypageService {
  http = inject(HttpClient);

  getAll(filters?: { authorId?: number, onlyMy?: boolean }) {
    let params: any = {};
    if (filters?.authorId) {
      params.authorId = filters.authorId;
    }
    if (filters?.onlyMy) {
      params.onlyMy = filters.onlyMy;
    }
    return this.http.get('/admin/mypage', { params })
  }

  getOne(id: number | string) {
    return this.http.get(`/admin/mypage/${id}`)
  }

  create(body: any) {
    return this.http.post('/admin/mypage', body)
  }

  removePage(id: number) {
    return this.http.delete(`/admin/mypage/${id}`)
  }

  getUsers() {
    return this.http.get('/admin/mypage/users')
  }

}
