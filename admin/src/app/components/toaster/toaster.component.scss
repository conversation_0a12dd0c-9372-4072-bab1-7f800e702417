// Toast container positioning
.toast-container {
  position: fixed;
  z-index: 9999;
  max-width: 400px;
  min-width: 300px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform, opacity;

  // Position variants
  &.top-left {
    top: 20px;
    left: 20px;
  }

  &.top-right {
    top: 20px;
    right: 20px;
  }

  &.bottom-left {
    bottom: 20px;
    left: 20px;
  }

  &.bottom-right {
    bottom: 20px;
    right: 20px;
  }

  &.bottom-middle {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
  }

  // Color variants
  &.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-left: 4px solid #047857;
    color: white;

    .toast-type-icon::before {
      content: "✓";
      color: white;
      font-weight: bold;
    }
  }

  &.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-left: 4px solid #b91c1c;
    color: white;

    .toast-type-icon::before {
      content: "✕";
      color: white;
      font-weight: bold;
    }
  }

  &.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-left: 4px solid #b45309;
    color: white;

    .toast-type-icon::before {
      content: "⚠";
      color: white;
      font-weight: bold;
    }
  }

  &.info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-left: 4px solid #1d4ed8;
    color: white;

    .toast-type-icon::before {
      content: "ℹ";
      color: white;
      font-weight: bold;
    }
  }

  &.default {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border-left: 4px solid #374151;
    color: white;

    .toast-type-icon::before {
      content: "•";
      color: white;
      font-weight: bold;
    }
  }
}

.toast-body {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-message {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);

  .text {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
  }
}

.toast-type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  font-size: 12px;
  font-weight: bold;
}

.close-toast-button {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 200ms ease;
  opacity: 0.7;

  &:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }

  svg {
    width: 14px;
    height: 14px;

    path {
      stroke: currentColor;
    }
  }
}

// Dark theme support
.dark .toast-container {
  &.success {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
  }

  &.error {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  }

  &.warning {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  }

  &.info {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  }

  &.default {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
  }
}

// Ensure animations work well with different positions
:host {
  contain: layout style paint;
}
