import { ToasterService } from '@/services/toaster.service';
import { Component, OnInit } from '@angular/core';
import { CommonModule } from "@angular/common";
import { IToast } from '@/interfaces/toast';
import { trigger, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-toaster',
  imports: [CommonModule],
  templateUrl: './toaster.component.html',
  styleUrl: './toaster.component.scss',

  animations: [
    trigger('toastAnimation', [
      transition(':enter', [
        style({
          opacity: 0,
          transform: 'translateY(20px) scale(0.95)'
        }),
        animate('300ms cubic-bezier(0.4, 0, 0.2, 1)', style({
          opacity: 1,
          transform: 'translateY(0) scale(1)'
        }))
      ]),
      transition(':leave', [
        animate('300ms cubic-bezier(0.4, 0, 0.2, 1)', style({
          opacity: 0,
          transform: 'translateY(-20px) scale(0.95)'
        }))
      ])
    ])
  ]
})
export class ToasterComponent implements OnInit {
  toasts$;

  constructor(private toasterService: ToasterService) {
    this.toasts$ = this.toasterService.toasts$;
  }

  ngOnInit(): void { }

  closeToast(toast: IToast): void {
    this.toasterService.removeToast(toast);
  }
}
