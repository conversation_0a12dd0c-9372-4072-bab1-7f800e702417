services:
  nginx:
    ports:
      - "8081:80"
    container_name: 'dev.sanatanadharma.world-nginx'

  server:
    container_name: 'dev.sanatanadharma.world-server'
    expose:
      - "9015"

  client:
    container_name: 'dev.sanatanadharma.world-client'
    build:
      target: development-ssr
    expose:
      - "9019"

  postgresql:
    container_name: 'dev.sanatanadharma.world-postgres'
    ports:
      - "54322:5432"
    volumes:
      - postgresql-data:/var/lib/postgresql/data

volumes:
  postgresql-data: